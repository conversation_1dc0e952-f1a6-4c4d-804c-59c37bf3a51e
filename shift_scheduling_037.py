from ortools.sat.python import cp_model
from openpyxl import Workbook
from openpyxl.formula.translate import Translator
from datetime import datetime
from tqdm import tqdm
import csv

PREFERRED_SHIFT_HOURS = 5  # change this to your preferred shift length
MAX_SHIFTS_PER_STAFF = 1  # maximum number of shifts a staff member can work per day

# Global scheduling constants
MIN_SHIFT_HOURS = 2
MAX_SHIFT_HOURS = 6
NUM_STAFF = 100 # total number of staff positions available
NUM_HOURS = 24  # hours in a day
DEFAULT_CSV_FILENAME = 'shift_names.csv'
DAYS_OF_WEEK = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']

def load_staff_names(csv_filename=DEFAULT_CSV_FILENAME):
    """Load staff names from CSV file and return as a list"""
    staff_names = []
    try:
        with open(csv_filename, 'r') as csv_file:
            reader = csv.reader(csv_file)
            for row in reader:
                if row and row[0].strip():  # Skip empty rows
                    staff_names.append(row[0].strip().strip('"'))
    except FileNotFoundError:
        print(f"Warning: {csv_filename} not found. Using staff numbers instead.")
    return staff_names

def get_staff_display_name(staff_id, staff_names):
    """Get display name for staff - use name if available, otherwise use staff number"""
    if staff_id < len(staff_names):
        return staff_names[staff_id]
    else:
        return f"Staff {staff_id + 1}"

def read_staff_requirements_from_csv(csv_filename, day_name):
    """Read staff requirements for a specific day from CSV file"""
    import csv
    with open(csv_filename, 'r') as csv_file:
        reader = csv.reader(csv_file)
        current_day = None
        staff_requirements = []

        for row in reader:
            if len(row) >= 1:
                # Check if this row contains a day name
                if row[0] in DAYS_OF_WEEK:
                    current_day = row[0]
                    if current_day == day_name:
                        staff_requirements = []
                # Check if this is a header row (Time, OPS)
                elif row[0] == 'Time' and len(row) >= 2:
                    continue  # Skip header rows
                # Check if this is a time/staff data row for our target day
                elif current_day == day_name and len(row) >= 2 and ':' in row[0]:
                    try:
                        staff_count = int(row[1]) if row[1] else 0
                        staff_requirements.append(staff_count)
                    except ValueError:
                        staff_requirements.append(0)

    return staff_requirements

def write_shift_schedule_to_xlsx(assignments, output_filename='staff_shifts_output.xlsx'):
    """Write shift assignments to Excel file in the format shown in the image"""

    # Load staff names
    staff_names = load_staff_names()

    # Get current date in the format shown in the image (YYYYMMDD)
    current_date = datetime.now().strftime('%Y%m%d')

    # Create a new workbook and get the active worksheet
    wb = Workbook()
    ws = wb.active
    ws.title = "Staff Shifts"

    # Write header rows
    ws.append(['Report', 'Staff Shifts', '', ''])
    ws.append(['Date', current_date, '', ''])
    ws.append(['Staff Name', 'Start Time', 'End Time', 'Total Hours'])

    # Sort assignments by start time first, then by staff ID to maintain chronological order
    sorted_assignments = sorted(assignments, key=lambda x: (x[0], x[1]))  # Sort by start time, then staff ID

    # Write assignments with staff formulas that reference Staff Names sheet
    for idx, (start_time, staff_id, duration) in enumerate(sorted_assignments):
        end_time = start_time + duration
        # Format times as HH:MM
        start_time_formatted = f"{start_time:02d}:00"
        end_time_formatted = f"{end_time:02d}:00"
        staff_formula = f"=VLOOKUP({staff_id},'Staff Names'!A:B,2,FALSE)"
        ws.append([staff_formula, start_time_formatted, end_time_formatted, duration])

    # Add Shift Count column after all data is populated
    # First add the header
    ws['E4'] = 'Shift Count'

    # Add shift count formulas for each data row
    data_start_row = 4  # First data row after headers
    data_end_row = data_start_row + len(sorted_assignments) - 1

    for row_idx in range(data_start_row, data_end_row + 1):
        shift_count_formula = f'=IF(COUNTIF(A{data_start_row}:A{data_end_row},A{row_idx})>1,"Multiple Shifts","Single Shift")'
        ws[f'E{row_idx}'] = shift_count_formula

    # Save the workbook
    wb.save(output_filename)
    print(f"Shift schedule written to {output_filename}")

def write_staff_calendar_to_xlsx(assignments, output_filename='staff_calendar_output.xlsx'):
    """Write staff assignments in calendar format with hours as columns"""

    # Load staff names
    staff_names = load_staff_names()

    # Get current date in the format shown in the image (YYYYMMDD)
    current_date = datetime.now().strftime('%Y%m%d')

    if not assignments:
        print("No assignments to write to calendar Excel file")
        return

    # Always use full 24-hour range (0-23)
    hour_range = list(range(24))

    # Create a mapping of hour -> list of staff working that hour
    hour_to_staff = {hour: [] for hour in hour_range}

    # Sort assignments and create staff formula mapping
    sorted_assignments = sorted(assignments, key=lambda x: (x[0], x[1]))

    for start_time, staff_id, duration in sorted_assignments:
        # Use direct cell reference to Staff Names sheet
        staff_formula = f"=VLOOKUP({staff_id},'Staff Names'!A:B,2,FALSE)"

        # Add this staff formula to all hours they work
        for hour in range(start_time, start_time + duration):
            if hour in hour_to_staff:
                hour_to_staff[hour].append(staff_formula)

    # Find maximum number of staff working in any single hour (for number of rows)
    max_staff_per_hour = max(len(staff_list) for staff_list in hour_to_staff.values()) if hour_to_staff else 0

    # Create a new workbook and get the active worksheet
    wb = Workbook()
    ws = wb.active
    ws.title = "Staff Calendar"

    # Write header rows
    header_row1 = ['Report', 'Staff Calendar'] + [''] * (len(hour_range) - 1)
    header_row2 = ['Date', current_date] + [''] * (len(hour_range) - 1)
    ws.append(header_row1)
    ws.append(header_row2)

    # Write hour headers - hours start from column A
    ws.append(hour_range)

    # Write staff assignments for each row
    for row_idx in range(max_staff_per_hour):
        row = []  # Start from column A
        for hour in hour_range:
            staff_list = hour_to_staff[hour]
            if row_idx < len(staff_list):
                row.append(staff_list[row_idx])
            else:
                row.append('')  # Empty cell if no staff at this position
        ws.append(row)

    # Save the workbook
    wb.save(output_filename)
    print(f"Staff calendar written to {output_filename}")

def write_combined_staff_workbook(assignments, staff_needed_per_hour, output_filename='staff_schedule.xlsx'):
    """Write both shift schedule and calendar to a single Excel workbook with separate sheets"""

    # Load staff names
    staff_names = load_staff_names()

    # Get current date in the format shown in the image (YYYYMMDD)
    current_date = datetime.now().strftime('%Y%m%d')

    if not assignments:
        print("No assignments to write to Excel workbook")
        return

    # Create a new workbook
    wb = Workbook()

    # === SHEET 1: Staff Shifts (List Format) ===
    ws_shifts = wb.active
    ws_shifts.title = "Staff Shifts"

    # Write header rows for shifts sheet
    ws_shifts.append(['Report', 'Staff Shifts', '', ''])
    ws_shifts.append(['Date', current_date, '', ''])
    ws_shifts.append(['Staff Name', 'Start Time', 'End Time', 'Total Hours'])

    # Sort assignments by start time first, then by staff ID to maintain chronological order
    sorted_assignments = sorted(assignments, key=lambda x: (x[0], x[1]))

    # Write assignments with staff formulas that reference Staff Names sheet
    for idx, (start_time, staff_id, duration) in enumerate(sorted_assignments):
        end_time = start_time + duration
        # Format times as HH:MM
        start_time_formatted = f"{start_time:02d}:00"
        end_time_formatted = f"{end_time:02d}:00"
        # Use direct cell reference to Staff Names sheet
        staff_formula = f"=VLOOKUP({staff_id},'Staff Names'!A:B,2,FALSE)"
        ws_shifts.append([staff_formula, start_time_formatted, end_time_formatted, duration])

    # Add Shift Count column after all data is populated
    # First add the header
    ws_shifts['E4'] = 'Shift Count'

    # Add shift count formulas for each data row
    data_start_row = 4  # First data row after headers
    data_end_row = data_start_row + len(sorted_assignments) - 1

    for row_idx in range(data_start_row, data_end_row + 1):
        shift_count_formula = f'=IF(COUNTIF(A{data_start_row}:A{data_end_row},A{row_idx})>1,"Multiple Shifts","Single Shift")'
        ws_shifts[f'E{row_idx}'] = shift_count_formula

    # === SHEET 2: Staff Calendar (Calendar Format) ===
    ws_calendar = wb.create_sheet("Staff Calendar")

    # Always use full 24-hour range (0-23)
    hour_range = list(range(24))

    # Create a mapping of hour -> list of staff working that hour
    hour_to_staff = {hour: [] for hour in hour_range}

    # Add staff formulas to working hours
    for start_time, staff_id, duration in sorted_assignments:
        # Use direct cell reference to Staff Names sheet
        staff_formula = f"=VLOOKUP({staff_id},'Staff Names'!A:B,2,FALSE)"

        # Add this staff formula to all hours they work
        for hour in range(start_time, start_time + duration):
            if hour in hour_to_staff:
                hour_to_staff[hour].append(staff_formula)

    # Find maximum number of staff working in any single hour (for number of rows)
    max_staff_per_hour = max(len(staff_list) for staff_list in hour_to_staff.values()) if hour_to_staff else 0

    # Write header rows for calendar sheet
    header_row1 = ['Report', 'Staff Calendar'] + [''] * (len(hour_range) - 1)
    header_row2 = ['Date', current_date] + [''] * (len(hour_range) - 1)
    ws_calendar.append(header_row1)
    ws_calendar.append(header_row2)

    # Write hour headers - hours start from column A
    ws_calendar.append(hour_range)

    # Write staff assignments for each row
    for row_idx in range(max_staff_per_hour):
        row = []  # Start from column A
        for hour in hour_range:
            staff_list = hour_to_staff[hour]
            if row_idx < len(staff_list):
                row.append(staff_list[row_idx])
            else:
                row.append('')  # Empty cell if no staff at this position
        ws_calendar.append(row)

    # === SHEET 3: Scheduled vs Required ===
    ws_comparison = wb.create_sheet("Scheduled vs Required")

    # Calculate scheduled staff per hour
    scheduled_per_hour = [0] * 24
    for start_time, staff_id, duration in sorted_assignments:
        for hour in range(start_time, start_time + duration):
            if hour < 24:
                scheduled_per_hour[hour] += 1

    # Write header rows for comparison sheet
    ws_comparison.append(['Report', 'Staff Scheduled vs Required'])
    ws_comparison.append(['Date', current_date])
    ws_comparison.append(['Hour of Day', 'Required', 'Scheduled', 'Difference'])

    # Write comparison data for each hour and check for understaffing
    understaffed_hours = []
    for hour in range(24):
        required = staff_needed_per_hour[hour] if hour < len(staff_needed_per_hour) else 0
        scheduled = scheduled_per_hour[hour]
        difference = scheduled - required
        ws_comparison.append([hour, required, scheduled, difference])

        # Track understaffed hours
        if scheduled < required:
            understaffed_hours.append((hour, required, scheduled, required - scheduled))

    # Add error information if understaffing detected
    if understaffed_hours:
        ws_comparison.append([])  # Empty row
        ws_comparison.append(['ERROR: UNDERSTAFFING DETECTED'])
        ws_comparison.append(['Hour', 'Required', 'Scheduled', 'Shortage'])
        for hour, required, scheduled, shortage in understaffed_hours:
            ws_comparison.append([f'Hour {hour}', required, scheduled, shortage])

    # === SHEET 4: Staff Names ===
    ws_names = wb.create_sheet("Staff Names")

    # Write header rows for staff names sheet
    ws_names.append(['Report', 'Staff Names'])
    ws_names.append(['Date', current_date])
    ws_names.append(['Staff ID', 'Staff Name'])

    # Write all staff positions (up to NUM_STAFF limit)
    for i in range(NUM_STAFF):
        if i < len(staff_names):
            ws_names.append([i, staff_names[i]])
        else:
            ws_names.append([i, f'Staff {i + 1}'])

    # Save the workbook
    wb.save(output_filename)
    print(f"Combined staff workbook written to {output_filename}")
    print(f"Sheets created: 'Staff Shifts', 'Staff Calendar', 'Scheduled vs Required', and 'Staff Names'")

def write_weekly_workbook_with_progress(all_assignments, all_requirements, output_filename='weekly_schedule.xlsx', pbar=None):
    """Write weekly schedule with all days Monday-Sunday in three sheets with progress tracking"""

    days_of_week = DAYS_OF_WEEK
    current_date = datetime.now().strftime('%Y%m%d')

    # Create a new workbook
    wb = Workbook()

    if pbar:
        pbar.set_description("Creating Staff Shifts sheet")
        pbar.update(1)

def write_weekly_workbook(all_assignments, all_requirements, output_filename='weekly_schedule.xlsx'):
    """Write weekly schedule with all days Monday-Sunday in three sheets"""

    # Load staff names
    staff_names = load_staff_names()

    days_of_week = DAYS_OF_WEEK
    current_date = datetime.now().strftime('%Y%m%d')

    # Create a new workbook
    wb = Workbook()

    # === SHEET 1: Staff Shifts (List Format) ===
    ws_shifts = wb.active
    ws_shifts.title = "Staff Shifts"

    # Write main header
    ws_shifts.append(['Report', 'Weekly Staff Shifts'])
    ws_shifts.append(['Date', current_date])
    ws_shifts.append([])  # Empty row

    # Store day information for later Shift Count column processing
    day_info = []

    # Process each day - populate all data first without Shift Count column
    for day in days_of_week:
        assignments = all_assignments.get(day, [])

        # Day header
        day_header_row = ws_shifts.max_row + 1
        ws_shifts.append([day])
        ws_shifts.append(['Staff Name', 'Start Time', 'End Time', 'Total Hours'])

        data_start_row = ws_shifts.max_row + 1  # Two rows below weekday header

        if assignments:
            # Sort assignments by start time
            sorted_assignments = sorted(assignments, key=lambda x: (x[0], x[1]))

            # Write staff assignments with formulas
            for idx, (start_time, staff_id, duration) in enumerate(sorted_assignments):
                end_time = start_time + duration
                start_time_formatted = f"{start_time:02d}:00"
                end_time_formatted = f"{end_time:02d}:00"
                # Use direct cell reference to Staff Names sheet
                staff_formula = f"=VLOOKUP({staff_id},'Staff Names'!A:B,2,FALSE)"
                ws_shifts.append([staff_formula, start_time_formatted, end_time_formatted, duration])

            data_end_row = ws_shifts.max_row
        else:
            ws_shifts.append(['No assignments'])
            data_end_row = ws_shifts.max_row

        # Store day information for Shift Count processing
        day_info.append({
            'day': day,
            'header_row': day_header_row + 1,  # Row with column headers
            'data_start_row': data_start_row,
            'data_end_row': data_end_row,
            'has_assignments': bool(assignments)
        })

        ws_shifts.append([])  # Empty row between days

    # Add Shift Count column after all data is populated
    for day_data in day_info:
        # Add Shift Count header
        ws_shifts.cell(row=day_data['header_row'] + 1, column=5, value='Shift Count')

        if day_data['has_assignments']:
            # Add shift count formulas for each data row
            for row_idx in range(day_data['data_start_row'], day_data['data_end_row'] + 1):
                # Check if the row has data (not empty in column A)
                if ws_shifts.cell(row=row_idx, column=1).value:
                    shift_count_formula = f'=IF(COUNTIF(A{day_data["data_start_row"]}:A{day_data["data_end_row"]},A{row_idx})>1,"Multiple Shifts","Single Shift")'
                    ws_shifts.cell(row=row_idx, column=5, value=shift_count_formula)

    # === SHEET 2: Staff Calendar (Calendar Format) ===
    ws_calendar = wb.create_sheet("Staff Calendar")

    # Write main header
    ws_calendar.append(['Report', 'Weekly Staff Calendar'])
    ws_calendar.append(['Date', current_date])
    ws_calendar.append([])  # Empty row

    # Hour range for headers
    hour_range = list(range(24))

    # Process each day
    for day in days_of_week:
        assignments = all_assignments.get(day, [])

        # Day header
        ws_calendar.append([day])

        # Hour headers
        ws_calendar.append(hour_range)

        if assignments:
            # Create hour to staff mapping
            hour_to_staff = {hour: [] for hour in hour_range}

            # Sort assignments and add staff names to working hours
            sorted_assignments = sorted(assignments, key=lambda x: (x[0], x[1]))

            for start_time, staff_id, duration in sorted_assignments:
                # Use direct cell reference to Staff Names sheet
                staff_formula = f"=VLOOKUP({staff_id},'Staff Names'!A:B,2,FALSE)"

                # Add staff formula to working hours
                for hour in range(start_time, start_time + duration):
                    if hour in hour_to_staff:
                        hour_to_staff[hour].append(staff_formula)

            # Find max staff per hour
            max_staff_per_hour = max(len(staff_list) for staff_list in hour_to_staff.values()) if hour_to_staff else 0

            # Write staff assignments for each row
            for row_idx in range(max_staff_per_hour):
                row = []
                for hour in hour_range:
                    staff_list = hour_to_staff[hour]
                    if row_idx < len(staff_list):
                        row.append(staff_list[row_idx])
                    else:
                        row.append('')
                ws_calendar.append(row)
        else:
            # Empty row if no assignments
            ws_calendar.append(['No assignments'] + [''] * 23)

        ws_calendar.append([])  # Empty row between days

    # === SHEET 3: Scheduled vs Required ===
    ws_comparison = wb.create_sheet("Scheduled vs Required")

    # Write main header
    ws_comparison.append(['Report', 'Weekly Scheduled vs Required'])
    ws_comparison.append(['Date', current_date])
    ws_comparison.append([])  # Empty row

    # Process each day
    for day in days_of_week:
        assignments = all_assignments.get(day, [])
        staff_needed_per_hour = all_requirements.get(day, [0] * 24)

        # Day header
        ws_comparison.append([day])
        ws_comparison.append(['Hour of Day', 'Required', 'Scheduled', 'Difference'])

        # Calculate scheduled staff per hour
        scheduled_per_hour = [0] * 24
        for start_time, staff_id, duration in assignments:
            for hour in range(start_time, start_time + duration):
                if hour < 24:
                    scheduled_per_hour[hour] += 1

        # Write comparison data
        understaffed_hours = []
        for hour in range(24):
            required = staff_needed_per_hour[hour] if hour < len(staff_needed_per_hour) else 0
            scheduled = scheduled_per_hour[hour]
            difference = scheduled - required
            ws_comparison.append([hour, required, scheduled, difference])

            # Track understaffed hours
            if scheduled < required:
                understaffed_hours.append((hour, required, scheduled, required - scheduled))

        # Add error information if understaffing detected
        if understaffed_hours:
            ws_comparison.append([])  # Empty row
            ws_comparison.append(['ERROR: UNDERSTAFFING DETECTED'])
            ws_comparison.append(['Hour', 'Required', 'Scheduled', 'Shortage'])
            for hour, required, scheduled, shortage in understaffed_hours:
                ws_comparison.append([f'Hour {hour}', required, scheduled, shortage])

        ws_comparison.append([])  # Empty row between days

    # === SHEET 4: Staff Names ===
    ws_names = wb.create_sheet("Staff Names")

    # Write header rows for staff names sheet
    ws_names.append(['Report', 'Staff Names'])
    ws_names.append(['Date', current_date])
    ws_names.append(['Staff ID', 'Staff Name'])

    # Write all staff positions (up to NUM_STAFF limit)
    for i in range(NUM_STAFF):
        if i < len(staff_names):
            ws_names.append([i, staff_names[i]])
        else:
            ws_names.append([i, f'Staff {i + 1}'])

    # Save the workbook
    wb.save(output_filename)
    print(f"Weekly workbook written to {output_filename}")
    print(f"Sheets created: 'Staff Shifts', 'Staff Calendar', 'Scheduled vs Required', and 'Staff Names' with all days Monday-Sunday")

# Complete the progress version of the function
def write_weekly_workbook_with_progress(all_assignments, all_requirements, output_filename='weekly_schedule.xlsx', pbar=None):
    """Write weekly schedule with all days Monday-Sunday in three sheets with progress tracking"""

    # Load staff names
    staff_names = load_staff_names()

    days_of_week = DAYS_OF_WEEK
    current_date = datetime.now().strftime('%Y%m%d')

    # Create a new workbook
    wb = Workbook()

    # === SHEET 1: Staff Shifts (List Format) ===
    ws_shifts = wb.active
    ws_shifts.title = "Staff Shifts"

    # Write main header
    ws_shifts.append(['Report', 'Weekly Staff Shifts'])
    ws_shifts.append(['Date', current_date])
    ws_shifts.append([])  # Empty row

    # Store day information for later Shift Count column processing
    day_info = []

    # Process each day - populate all data first without Shift Count column
    for day in days_of_week:
        assignments = all_assignments.get(day, [])

        # Day header
        day_header_row = ws_shifts.max_row + 1
        ws_shifts.append([day])
        ws_shifts.append(['Staff Name', 'Start Time', 'End Time', 'Total Hours'])

        data_start_row = ws_shifts.max_row + 1  # Two rows below weekday header

        if assignments:
            # Sort assignments by start time
            sorted_assignments = sorted(assignments, key=lambda x: (x[0], x[1]))

            # Write staff assignments with formulas
            for idx, (start_time, staff_id, duration) in enumerate(sorted_assignments):
                end_time = start_time + duration
                start_time_formatted = f"{start_time:02d}:00"
                end_time_formatted = f"{end_time:02d}:00"
                # Use direct cell reference to Staff Names sheet
                staff_formula = f"=VLOOKUP({staff_id},'Staff Names'!A:B,2,FALSE)"
                ws_shifts.append([staff_formula, start_time_formatted, end_time_formatted, duration])

            data_end_row = ws_shifts.max_row
        else:
            ws_shifts.append(['No assignments'])
            data_end_row = ws_shifts.max_row

        # Store day information for Shift Count processing
        day_info.append({
            'day': day,
            'header_row': day_header_row + 1,  # Row with column headers
            'data_start_row': data_start_row,
            'data_end_row': data_end_row,
            'has_assignments': bool(assignments)
        })

        ws_shifts.append([])  # Empty row between days

    # Add Shift Count column after all data is populated
    for day_data in day_info:
        # Add Shift Count header
        ws_shifts.cell(row=day_data['header_row'] + 1, column=5, value='Shift Count')

        if day_data['has_assignments']:
            # Add shift count formulas for each data row
            for row_idx in range(day_data['data_start_row'], day_data['data_end_row'] + 1):
                # Check if the row has data (not empty in column A)
                if ws_shifts.cell(row=row_idx, column=1).value:
                    shift_count_formula = f'=IF(COUNTIF(A{day_data["data_start_row"]}:A{day_data["data_end_row"]},A{row_idx})>1,"Multiple Shifts","Single Shift")'
                    ws_shifts.cell(row=row_idx, column=5, value=shift_count_formula)

    if pbar:
        pbar.set_description("Creating Staff Calendar sheet")
        pbar.update(1)

    # === SHEET 2: Staff Calendar (Calendar Format) ===
    ws_calendar = wb.create_sheet("Staff Calendar")

    # Write main header
    ws_calendar.append(['Report', 'Weekly Staff Calendar'])
    ws_calendar.append(['Date', current_date])
    ws_calendar.append([])  # Empty row

    # Hour range for headers
    hour_range = list(range(24))

    # Process each day
    for day in days_of_week:
        assignments = all_assignments.get(day, [])

        # Day header
        ws_calendar.append([day])

        # Hour headers
        ws_calendar.append(hour_range)

        if assignments:
            # Create hour to staff mapping
            hour_to_staff = {hour: [] for hour in hour_range}

            # Sort assignments and add staff names to working hours
            sorted_assignments = sorted(assignments, key=lambda x: (x[0], x[1]))

            for start_time, staff_id, duration in sorted_assignments:
                # Use direct cell reference to Staff Names sheet
                staff_formula = f"=VLOOKUP({staff_id},'Staff Names'!A:B,2,FALSE)"

                # Add staff formula to working hours
                for hour in range(start_time, start_time + duration):
                    if hour in hour_to_staff:
                        hour_to_staff[hour].append(staff_formula)

            # Find max staff per hour
            max_staff_per_hour = max(len(staff_list) for staff_list in hour_to_staff.values()) if hour_to_staff else 0

            # Write staff assignments for each row
            for row_idx in range(max_staff_per_hour):
                row = []
                for hour in hour_range:
                    staff_list = hour_to_staff[hour]
                    if row_idx < len(staff_list):
                        row.append(staff_list[row_idx])
                    else:
                        row.append('')
                ws_calendar.append(row)
        else:
            # Empty row if no assignments
            ws_calendar.append(['No assignments'] + [''] * 23)

        ws_calendar.append([])  # Empty row between days

    if pbar:
        pbar.set_description("Creating Scheduled vs Required sheet")
        pbar.update(1)

    # === SHEET 3: Scheduled vs Required ===
    ws_comparison = wb.create_sheet("Scheduled vs Required")

    # Write main header
    ws_comparison.append(['Report', 'Weekly Scheduled vs Required'])
    ws_comparison.append(['Date', current_date])
    ws_comparison.append([])  # Empty row

    # Process each day
    for day in days_of_week:
        assignments = all_assignments.get(day, [])
        staff_needed_per_hour = all_requirements.get(day, [0] * 24)

        # Day header
        ws_comparison.append([day])
        ws_comparison.append(['Hour of Day', 'Required', 'Scheduled', 'Difference'])

        # Calculate scheduled staff per hour
        scheduled_per_hour = [0] * 24
        for start_time, staff_id, duration in assignments:
            for hour in range(start_time, start_time + duration):
                if hour < 24:
                    scheduled_per_hour[hour] += 1

        # Write comparison data
        understaffed_hours = []
        for hour in range(24):
            required = staff_needed_per_hour[hour] if hour < len(staff_needed_per_hour) else 0
            scheduled = scheduled_per_hour[hour]
            difference = scheduled - required
            ws_comparison.append([hour, required, scheduled, difference])

            # Track understaffed hours
            if scheduled < required:
                understaffed_hours.append((hour, required, scheduled, required - scheduled))

        # Add error information if understaffing detected
        if understaffed_hours:
            ws_comparison.append([])  # Empty row
            ws_comparison.append(['ERROR: UNDERSTAFFING DETECTED'])
            ws_comparison.append(['Hour', 'Required', 'Scheduled', 'Shortage'])
            for hour, required, scheduled, shortage in understaffed_hours:
                ws_comparison.append([f'Hour {hour}', required, scheduled, shortage])

        ws_comparison.append([])  # Empty row between days

    # === SHEET 4: Staff Names ===
    ws_names = wb.create_sheet("Staff Names")

    # Write header rows for staff names sheet
    ws_names.append(['Report', 'Staff Names'])
    ws_names.append(['Date', current_date])
    ws_names.append(['Staff ID', 'Staff Name'])

    # Write all staff positions (up to NUM_STAFF limit)
    for i in range(NUM_STAFF):
        if i < len(staff_names):
            ws_names.append([i, staff_names[i]])
        else:
            ws_names.append([i, f'Staff {i + 1}'])

    # Save the workbook
    wb.save(output_filename)

    if pbar:
        pbar.set_description("Workbook saved successfully")

def create_weekly_workbook(csv_filename='Optix Shifts_Gideon.csv', output_filename='weekly_schedule.xlsx'):
    """Create a single workbook with all days Monday-Sunday in three sheets"""
    days_of_week = DAYS_OF_WEEK

    print(f"Creating weekly workbook from {csv_filename}...")
    print("=" * 50)

    # Store all assignments and requirements for each day
    all_assignments = {}  # day -> assignments
    all_requirements = {}  # day -> staff_needed_per_hour
    weekly_staff_hours = {}  # staff_id -> total_hours_worked

    # Process each day with progress bar
    with tqdm(total=len(days_of_week), desc="Processing days", unit="day") as pbar:
        for day in days_of_week:
            pbar.set_description(f"Processing {day}")

            try:
                # Get staff requirements for this day
                staff_needed_per_hour = read_staff_requirements_from_csv(csv_filename, day)
                if not staff_needed_per_hour:
                    staff_needed_per_hour = [0] * 24

                all_requirements[day] = staff_needed_per_hour

                # Solve scheduling for this day (start fresh each day)
                assignments = solve_shift_scheduling_balanced_no_output(csv_filename, day, {})

                if assignments:
                    all_assignments[day] = assignments
                    pbar.set_postfix(status="✓ Success")
                else:
                    all_assignments[day] = []
                    pbar.set_postfix(status="✗ No solution")

            except Exception as e:
                all_assignments[day] = []
                all_requirements[day] = [0] * 24
                pbar.set_postfix(status=f"✗ Error: {str(e)[:20]}...")

            pbar.update(1)

    # Create the combined weekly workbook with progress
    print("\nGenerating Excel workbook...")
    with tqdm(total=3, desc="Creating sheets", unit="sheet") as pbar:
        pbar.set_description("Creating workbook structure")
        pbar.update(1)

        write_weekly_workbook_with_progress(all_assignments, all_requirements, output_filename, pbar)

    print("\n" + "=" * 50)
    print(f"Weekly workbook created: {output_filename}")

def process_all_days_balanced(csv_filename='Optix Shifts_Gideon.csv'):
    """Process all days with balanced staff hours across the week"""
    days_of_week = DAYS_OF_WEEK

    print(f"Processing all days from {csv_filename} with balanced weekly scheduling...")
    print("=" * 50)

    # Track staff hours across the week
    weekly_staff_hours = {}  # staff_id -> total_hours_worked

    # Process with progress bar
    with tqdm(total=len(days_of_week), desc="Processing days", unit="day") as pbar:
        for day in days_of_week:
            pbar.set_description(f"Processing {day}")
            output_filename = f"{day.lower()}_shifts_schedule.xlsx"

            try:
                assignments = solve_shift_scheduling_balanced(csv_filename, day, output_filename, {})
                if assignments:
                    pbar.set_postfix(status="✓ Success")
                else:
                    pbar.set_postfix(status="✗ No solution")
            except Exception as e:
                pbar.set_postfix(status=f"✗ Error: {str(e)[:20]}...")

            pbar.update(1)

    print("\n" + "=" * 50)
    print("All days processed with balanced weekly scheduling.")

def process_all_days(csv_filename='Optix Shifts_Gideon.csv'):
    """Process all days of the week and create separate Excel files for each day"""
    days_of_week = DAYS_OF_WEEK

    print(f"Processing all days from {csv_filename}...")
    print("=" * 50)

    for day in days_of_week:
        print(f"\nProcessing {day}...")
        output_filename = f"{day.lower()}_shifts_schedule.xlsx"

        try:
            assignments = solve_shift_scheduling(csv_filename, day, output_filename)
            if assignments:
                print(f"✓ {day} schedule created successfully: {output_filename}")
            else:
                print(f"✗ No solution found for {day}")
                print(f"  Consider increasing MAX_SHIFTS_PER_STAFF or NUM_STAFF for {day}")
        except Exception as e:
            print(f"✗ Error processing {day}: {str(e)}")
            print(f"  This may indicate understaffing issues for {day}")

    print("\n" + "=" * 50)
    print("All days processed. Excel files created for each day with reusable weekly schedules.")

def solve_shift_scheduling_balanced_no_output(csv_filename=None, day_name=None, weekly_staff_hours=None):
    """Solve shift scheduling with weekly hour balancing without creating output file"""
    if weekly_staff_hours is None:
        weekly_staff_hours = {}

    # Load staff names to determine priority
    staff_names = load_staff_names()
    num_named_staff = len(staff_names)

    model = cp_model.CpModel()

    # Read staff requirements from CSV if provided, otherwise use default
    if csv_filename and day_name:
        staff_needed_per_hour = read_staff_requirements_from_csv(csv_filename, day_name)
        if not staff_needed_per_hour:
            staff_needed_per_hour = [0] * 24
    else:
        # Default fallback data
        staff_needed_per_hour = [
            0,# 00:00
            0,# 01:00
            2,# 02:00
            2,# 03:00
            5,# 04:00
            9,# 05:00
            13,# 06:00
            18,# 07:00
            15,# 08:00
            15,# 09:00
            13,# 10:00
            13,# 11:00
            12,# 12:00
            12,# 13:00
            5,# 14:00
            6,# 15:00
            7,# 16:00
            2,# 17:00
            1,# 18:00
            0,# 19:00
            0,# 20:00
            0,# 21:00
            0,# 22:00
            0# 23:00
        ]

    staff_range = range(NUM_STAFF)
    hour_range = range(NUM_HOURS)

    # --- VARIABLES ---
    shifts = {}
    for s in staff_range:
        for start in hour_range:
            for duration in range(MIN_SHIFT_HOURS, MAX_SHIFT_HOURS + 1):
                if start + duration <= NUM_HOURS:
                    shifts[(s, start, duration)] = model.NewBoolVar(
                        f'shift_s{s}_st{start}_d{duration}'
                    )

    # --- CONSTRAINTS ---
    for s in staff_range:
        model.Add(
            sum([
                shifts[(s, start, duration)]
                for start in hour_range
                for duration in range(MIN_SHIFT_HOURS, MAX_SHIFT_HOURS + 1)
                if start + duration <= NUM_HOURS
            ]) <= MAX_SHIFTS_PER_STAFF
        )

    # Ensure shifts don't overlap for the same staff member
    for s in staff_range:
        for h in hour_range:
            overlapping_shifts = []
            for start in hour_range:
                for duration in range(MIN_SHIFT_HOURS, MAX_SHIFT_HOURS + 1):
                    if start <= h < start + duration and start + duration <= NUM_HOURS:
                        if (s, start, duration) in shifts:
                            overlapping_shifts.append(shifts[(s, start, duration)])
            if overlapping_shifts:
                model.Add(sum(overlapping_shifts) <= 1)

    # SOFT CONSTRAINT: Try to meet exact staffing, but allow closest possible if exact is impossible
    shortage_vars = {}
    excess_vars = {}

    for h in hour_range:
        staff_on_duty = []
        for s in staff_range:
            for start in hour_range:
                for duration in range(MIN_SHIFT_HOURS, MAX_SHIFT_HOURS + 1):
                    if start <= h < start + duration:
                        if (s, start, duration) in shifts:
                            staff_on_duty.append(shifts[(s, start, duration)])

        shortage_vars[h] = model.NewIntVar(0, staff_needed_per_hour[h], f'shortage_{h}')
        excess_vars[h] = model.NewIntVar(0, NUM_STAFF, f'excess_{h}')

        model.Add(sum(staff_on_duty) + shortage_vars[h] - excess_vars[h] == staff_needed_per_hour[h])

    # --- OBJECTIVE ---
    objective_terms = []

    # PRIORITY 1: Minimize staffing deviations
    for h in hour_range:
        objective_terms.append(1000 * shortage_vars[h])
        objective_terms.append(100 * excess_vars[h])

    # PRIORITY 2: Prefer PREFERRED_SHIFT_HOURS
    for (s, start, duration) in shifts:
        shift_preference_cost = ((duration - PREFERRED_SHIFT_HOURS)**2 + 1) * shifts[(s, start, duration)]
        objective_terms.append(shift_preference_cost)

    # PRIORITY 3: Prioritize named staff over unnamed staff
    for s in staff_range:
        if s >= num_named_staff:  # Unnamed staff get penalty
            staff_priority_penalty = 50  # Penalty for using unnamed staff
            for start in hour_range:
                for duration in range(MIN_SHIFT_HOURS, MAX_SHIFT_HOURS + 1):
                    if (s, start, duration) in shifts:
                        objective_terms.append(staff_priority_penalty * shifts[(s, start, duration)])

    # PRIORITY 4: Prioritize staff in numerical order (0, 1, 2, etc.)
    for s in staff_range:
        staff_order_penalty = s  # Lower staff numbers get lower penalty

        for start in hour_range:
            for duration in range(MIN_SHIFT_HOURS, MAX_SHIFT_HOURS + 1):
                if (s, start, duration) in shifts:
                    objective_terms.append(staff_order_penalty * shifts[(s, start, duration)])

    model.Minimize(sum(objective_terms))

    # --- SOLVE ---
    solver = cp_model.CpSolver()
    status = solver.Solve(model)

    # --- OUTPUT ---
    if status in (cp_model.OPTIMAL, cp_model.FEASIBLE):
        assignments = []
        for s in staff_range:
            for start in hour_range:
                for duration in range(MIN_SHIFT_HOURS, MAX_SHIFT_HOURS + 1):
                    if (s, start, duration) in shifts and solver.Value(shifts[(s, start, duration)]) == 1:
                        assignments.append((start, s, duration))

        assignments.sort(key=lambda x: (x[0], x[1]))
        return assignments
    else:
        return []

def solve_shift_scheduling_balanced(csv_filename=None, day_name=None, output_xlsx_filename='staff_shifts_output.xlsx', weekly_staff_hours=None):
    """Solve shift scheduling with weekly hour balancing"""
    if weekly_staff_hours is None:
        weekly_staff_hours = {}

    # Load staff names to determine priority
    staff_names = load_staff_names()
    num_named_staff = len(staff_names)

    model = cp_model.CpModel()

    # Read staff requirements from CSV if provided, otherwise use default
    if csv_filename and day_name:
        staff_needed_per_hour = read_staff_requirements_from_csv(csv_filename, day_name)
        print(f"Reading staff requirements for {day_name} from {csv_filename}")
    else:
        # Default fallback data
        staff_needed_per_hour = [
            0,# 00:00
            0,# 01:00
            2,# 02:00
            2,# 03:00
            5,# 04:00
            9,# 05:00
            13,# 06:00
            18,# 07:00
            15,# 08:00
            15,# 09:00
            13,# 10:00
            13,# 11:00
            12,# 12:00
            12,# 13:00
            5,# 14:00
            6,# 15:00
            7,# 16:00
            2,# 17:00
            1,# 18:00
            0,# 19:00
            0,# 20:00
            0,# 21:00
            0,# 22:00
            0# 23:00
        ]
        print("Using default staff requirements")

    print(f"Staff requirements: {staff_needed_per_hour}")
    print(f"Total demand hours: {sum(staff_needed_per_hour)}")
    print()

    staff_range = range(NUM_STAFF)
    hour_range = range(NUM_HOURS)

    # --- VARIABLES ---
    shifts = {}
    for s in staff_range:
        for start in hour_range:
            for duration in range(MIN_SHIFT_HOURS, MAX_SHIFT_HOURS + 1):
                if start + duration <= NUM_HOURS:
                    shifts[(s, start, duration)] = model.NewBoolVar(
                        f'shift_s{s}_st{start}_d{duration}'
                    )

    # --- CONSTRAINTS ---
    # Modified constraint: Allow each staff member to work up to MAX_SHIFTS_PER_STAFF shifts
    for s in staff_range:
        model.Add(
            sum([
                shifts[(s, start, duration)]
                for start in hour_range
                for duration in range(MIN_SHIFT_HOURS, MAX_SHIFT_HOURS + 1)
                if start + duration <= NUM_HOURS
            ]) <= MAX_SHIFTS_PER_STAFF
        )

    # Ensure shifts don't overlap for the same staff member
    for s in staff_range:
        for h in hour_range:
            overlapping_shifts = []
            for start in hour_range:
                for duration in range(MIN_SHIFT_HOURS, MAX_SHIFT_HOURS + 1):
                    if start <= h < start + duration and start + duration <= NUM_HOURS:
                        if (s, start, duration) in shifts:
                            overlapping_shifts.append(shifts[(s, start, duration)])
            if overlapping_shifts:
                model.Add(sum(overlapping_shifts) <= 1)

    # SOFT CONSTRAINT: Try to meet exact staffing, but allow closest possible if exact is impossible
    shortage_vars = {}
    excess_vars = {}

    for h in hour_range:
        staff_on_duty = []
        for s in staff_range:
            for start in hour_range:
                for duration in range(MIN_SHIFT_HOURS, MAX_SHIFT_HOURS + 1):
                    if start <= h < start + duration:
                        if (s, start, duration) in shifts:
                            staff_on_duty.append(shifts[(s, start, duration)])

        # Create shortage and excess variables for soft constraints
        shortage_vars[h] = model.NewIntVar(0, staff_needed_per_hour[h], f'shortage_{h}')
        excess_vars[h] = model.NewIntVar(0, NUM_STAFF, f'excess_{h}')

        # Soft constraint: scheduled + shortage - excess = required
        model.Add(sum(staff_on_duty) + shortage_vars[h] - excess_vars[h] == staff_needed_per_hour[h])

    # --- OBJECTIVE: Minimize staffing deviations first, then balance weekly hours ---
    objective_terms = []

    # PRIORITY 1: Minimize staffing deviations (shortage and excess)
    # Heavy penalty for not meeting exact requirements
    for h in hour_range:
        # Shortage is worse than excess, so higher penalty
        objective_terms.append(1000 * shortage_vars[h])  # Very high penalty for understaffing
        objective_terms.append(100 * excess_vars[h])     # Lower penalty for overstaffing

    # PRIORITY 2: Prefer PREFERRED_SHIFT_HOURS
    for (s, start, duration) in shifts:
        shift_preference_cost = ((duration - PREFERRED_SHIFT_HOURS)**2 + 1) * shifts[(s, start, duration)]
        objective_terms.append(shift_preference_cost)

    # PRIORITY 3: Prioritize named staff over unnamed staff
    for s in staff_range:
        if s >= num_named_staff:  # Unnamed staff get penalty
            staff_priority_penalty = 50  # Penalty for using unnamed staff
            for start in hour_range:
                for duration in range(MIN_SHIFT_HOURS, MAX_SHIFT_HOURS + 1):
                    if (s, start, duration) in shifts:
                        objective_terms.append(staff_priority_penalty * shifts[(s, start, duration)])

    # PRIORITY 4: Prioritize staff in numerical order (0, 1, 2, etc.)
    for s in staff_range:
        staff_order_penalty = s  # Lower staff numbers get lower penalty

        for start in hour_range:
            for duration in range(MIN_SHIFT_HOURS, MAX_SHIFT_HOURS + 1):
                if (s, start, duration) in shifts:
                    objective_terms.append(staff_order_penalty * shifts[(s, start, duration)])

    model.Minimize(sum(objective_terms))

    # --- SOLVE ---
    solver = cp_model.CpSolver()
    status = solver.Solve(model)

    # --- OUTPUT ---
    if status in (cp_model.OPTIMAL, cp_model.FEASIBLE):
        print('Solution found!')
        print(f'Total weighted cost: {solver.ObjectiveValue():.0f}')
        print(f'--- Staff Assignments (sorted by start time then staff id) ---')

        assignments = []
        for s in staff_range:
            for start in hour_range:
                for duration in range(MIN_SHIFT_HOURS, MAX_SHIFT_HOURS + 1):
                    if (s, start, duration) in shifts and solver.Value(shifts[(s, start, duration)]) == 1:
                        assignments.append((start, s, duration))

        # sort by start time then staff ID
        assignments.sort(key=lambda x: (x[0], x[1]))

        # Track staff usage for summary
        staff_shift_count = {}
        for start, s, duration in assignments:
            end = start + duration
            print(f'Staff {s}: Works from hour {start}:00 to {end}:00 ({duration} hours)')
            staff_shift_count[s] = staff_shift_count.get(s, 0) + 1

        assigned_staff = {s for _, s, _ in assignments}
        for s in staff_range:
            if s not in assigned_staff:
                print(f'Staff {s}: Off duty')

        # Summary statistics
        print(f'\n--- Summary ---')
        print(f'Total shifts assigned: {len(assignments)}')
        staff_with_multiple_shifts = sum(1 for count in staff_shift_count.values() if count > 1)
        print(f'Staff working multiple shifts: {staff_with_multiple_shifts}')

        if staff_with_multiple_shifts > 0:
            print('Staff working multiple shifts:')
            for s, count in staff_shift_count.items():
                if count > 1:
                    print(f'  Staff {s}: {count} shifts')

        # Calculate actual staffing and deviations
        scheduled_per_hour = [0] * 24
        for start_time, staff_id, duration in assignments:
            for hour in range(start_time, start_time + duration):
                if hour < 24:
                    scheduled_per_hour[hour] += 1

        # Get the actual shortage and excess values from the solver
        total_shortage = 0
        total_excess = 0
        understaffed_hours = []
        overstaffed_hours = []

        for hour in range(24):
            required = staff_needed_per_hour[hour] if hour < len(staff_needed_per_hour) else 0
            scheduled = scheduled_per_hour[hour]
            shortage = solver.Value(shortage_vars[hour])
            excess = solver.Value(excess_vars[hour])

            total_shortage += shortage
            total_excess += excess

            if shortage > 0:
                understaffed_hours.append((hour, required, scheduled, shortage))
            elif excess > 0:
                overstaffed_hours.append((hour, required, scheduled, excess))

        # Write results to combined Excel workbook with multiple sheets
        write_combined_staff_workbook(assignments, staff_needed_per_hour, output_xlsx_filename)

        print(f"\nExcel workbook created: {output_xlsx_filename}")
        print(f"Contains sheets: 'Staff Shifts' (list format), 'Staff Calendar' (calendar format), and 'Scheduled vs Required' (comparison)")

        # Report staffing accuracy with closest possible results
        if total_shortage == 0 and total_excess == 0:
            print(f"✅ Perfect staffing match! All differences are 0.")
        else:
            print(f"\n📊 Closest possible staffing achieved:")
            print(f"Total shortage: {total_shortage} staff-hours")
            print(f"Total excess: {total_excess} staff-hours")

            if understaffed_hours:
                print(f"\nUnderstaffed hours (closest possible):")
                for hour, required, scheduled, shortage in understaffed_hours:
                    print(f"  Hour {hour:2d}: Required {required}, Scheduled {scheduled}, Short by {shortage}")

            if overstaffed_hours:
                print(f"\nOverstaffed hours:")
                for hour, required, scheduled, excess in overstaffed_hours:
                    print(f"  Hour {hour:2d}: Required {required}, Scheduled {scheduled}, Excess by {excess}")

            print(f"\n💡 This is the closest possible match given the constraints.")

        return assignments

    elif status == cp_model.INFEASIBLE:
        print('No solution found. The exact staffing requirements cannot be met.')
        print('Consider:')
        print('- Increasing MAX_SHIFTS_PER_STAFF')
        print('- Increasing NUM_STAFF')
        print('- Adjusting min/max shift hours')
        print('- Reviewing if the staffing requirements are feasible')
        return []
    else:
        print('Error occurred during solving.')
        return []

def solve_shift_scheduling(csv_filename=None, day_name=None, output_xlsx_filename='staff_shifts_output.xlsx'):
    # Load staff names to determine priority
    staff_names = load_staff_names()
    num_named_staff = len(staff_names)

    model = cp_model.CpModel()

    # Read staff requirements from CSV if provided, otherwise use default
    if csv_filename and day_name:
        staff_needed_per_hour = read_staff_requirements_from_csv(csv_filename, day_name)
        print(f"Reading staff requirements for {day_name} from {csv_filename}")
    else:
        # Default fallback data
        staff_needed_per_hour = [
            0,# 00:00
            0,# 01:00
            2,# 02:00
            2,# 03:00
            5,# 04:00
            9,# 05:00
            13,# 06:00
            18,# 07:00
            15,# 08:00
            15,# 09:00
            13,# 10:00
            13,# 11:00
            12,# 12:00
            12,# 13:00
            5,# 14:00
            6,# 15:00
            7,# 16:00
            2,# 17:00
            1,# 18:00
            0,# 19:00
            0,# 20:00
            0,# 21:00
            0,# 22:00
            0# 23:00
        ]
        print("Using default staff requirements")

    print(f"Staff requirements: {staff_needed_per_hour}")
    print(f"Total demand hours: {sum(staff_needed_per_hour)}")
    print()

    staff_range = range(NUM_STAFF)
    hour_range = range(NUM_HOURS)

    # --- VARIABLES ---
    shifts = {}
    for s in staff_range:
        for start in hour_range:
            for duration in range(MIN_SHIFT_HOURS, MAX_SHIFT_HOURS + 1):
                if start + duration <= NUM_HOURS:
                    shifts[(s, start, duration)] = model.NewBoolVar(
                        f'shift_s{s}_st{start}_d{duration}'
                    )

    # --- CONSTRAINTS ---
    # Modified constraint: Allow each staff member to work up to MAX_SHIFTS_PER_STAFF shifts
    for s in staff_range:
        model.Add(
            sum([
                shifts[(s, start, duration)]
                for start in hour_range
                for duration in range(MIN_SHIFT_HOURS, MAX_SHIFT_HOURS + 1)
                if start + duration <= NUM_HOURS
            ]) <= MAX_SHIFTS_PER_STAFF
        )

    # Ensure shifts don't overlap for the same staff member
    for s in staff_range:
        for h in hour_range:
            overlapping_shifts = []
            for start in hour_range:
                for duration in range(MIN_SHIFT_HOURS, MAX_SHIFT_HOURS + 1):
                    if start <= h < start + duration and start + duration <= NUM_HOURS:
                        if (s, start, duration) in shifts:
                            overlapping_shifts.append(shifts[(s, start, duration)])
            if overlapping_shifts:
                model.Add(sum(overlapping_shifts) <= 1)

    # Ensure minimum staffing requirements are met
    for h in hour_range:
        staff_on_duty = []
        for s in staff_range:
            for start in hour_range:
                for duration in range(MIN_SHIFT_HOURS, MAX_SHIFT_HOURS + 1):
                    if start <= h < start + duration:
                        if (s, start, duration) in shifts:
                            staff_on_duty.append(shifts[(s, start, duration)])
        model.Add(sum(staff_on_duty) >= staff_needed_per_hour[h])

    # --- OBJECTIVE: prefer PREFERRED_SHIFT_HOURS and minimize total shifts ---
    objective_terms = []

    # PRIORITY 1: Prefer PREFERRED_SHIFT_HOURS
    for (s, start, duration) in shifts:
        shift_preference_cost = ((duration - PREFERRED_SHIFT_HOURS)**2 + 1) * shifts[(s, start, duration)]
        objective_terms.append(shift_preference_cost)

    # PRIORITY 2: Prioritize named staff over unnamed staff
    for s in staff_range:
        if s >= num_named_staff:  # Unnamed staff get penalty
            staff_priority_penalty = 50  # Penalty for using unnamed staff
            for start in hour_range:
                for duration in range(MIN_SHIFT_HOURS, MAX_SHIFT_HOURS + 1):
                    if start + duration <= NUM_HOURS and (s, start, duration) in shifts:
                        objective_terms.append(staff_priority_penalty * shifts[(s, start, duration)])

    # PRIORITY 3: Prioritize staff in numerical order (0, 1, 2, etc.)
    for s in staff_range:
        staff_order_penalty = s  # Lower staff numbers get lower penalty
        for start in hour_range:
            for duration in range(MIN_SHIFT_HOURS, MAX_SHIFT_HOURS + 1):
                if start + duration <= NUM_HOURS and (s, start, duration) in shifts:
                    objective_terms.append(staff_order_penalty * shifts[(s, start, duration)])

    # PRIORITY 4: Minimize total shifts (penalty for using multiple shifts per staff member)
    for s in staff_range:
        for start in hour_range:
            for duration in range(MIN_SHIFT_HOURS, MAX_SHIFT_HOURS + 1):
                if start + duration <= NUM_HOURS and (s, start, duration) in shifts:
                    objective_terms.append(10 * shifts[(s, start, duration)])

    model.Minimize(sum(objective_terms))

    # --- SOLVE ---
    solver = cp_model.CpSolver()
    status = solver.Solve(model)

    # --- OUTPUT ---
    if status in (cp_model.OPTIMAL, cp_model.FEASIBLE):
        print('Solution found!')
        print(f'Total weighted cost: {solver.ObjectiveValue():.0f}')
        print(f'--- Staff Assignments (sorted by start time then staff id) ---')

        assignments = []
        for s in staff_range:
            for start in hour_range:
                for duration in range(MIN_SHIFT_HOURS, MAX_SHIFT_HOURS + 1):
                    if (s, start, duration) in shifts and solver.Value(shifts[(s, start, duration)]) == 1:
                        assignments.append((start, s, duration))

        # sort by start time then staff ID
        assignments.sort(key=lambda x: (x[0], x[1]))

        # Track staff usage for summary
        staff_shift_count = {}
        for start, s, duration in assignments:
            end = start + duration
            print(f'Staff {s}: Works from hour {start}:00 to {end}:00 ({duration} hours)')
            staff_shift_count[s] = staff_shift_count.get(s, 0) + 1

        assigned_staff = {s for _, s, _ in assignments}
        for s in staff_range:
            if s not in assigned_staff:
                print(f'Staff {s}: Off duty')

        # Summary statistics
        print(f'\n--- Summary ---')
        print(f'Total shifts assigned: {len(assignments)}')
        staff_with_multiple_shifts = sum(1 for count in staff_shift_count.values() if count > 1)
        print(f'Staff working multiple shifts: {staff_with_multiple_shifts}')

        if staff_with_multiple_shifts > 0:
            print('Staff working multiple shifts:')
            for s, count in staff_shift_count.items():
                if count > 1:
                    print(f'  Staff {s}: {count} shifts')

        # Check for understaffing before writing results
        scheduled_per_hour = [0] * 24
        for start_time, staff_id, duration in assignments:
            for hour in range(start_time, start_time + duration):
                if hour < 24:
                    scheduled_per_hour[hour] += 1

        understaffed_hours = []
        for hour in range(24):
            required = staff_needed_per_hour[hour] if hour < len(staff_needed_per_hour) else 0
            scheduled = scheduled_per_hour[hour]
            if scheduled < required:
                understaffed_hours.append((hour, required, scheduled, required - scheduled))

        # Write results to combined Excel workbook with multiple sheets
        write_combined_staff_workbook(assignments, staff_needed_per_hour, output_xlsx_filename)

        print(f"\nExcel workbook created: {output_xlsx_filename}")
        print(f"Contains sheets: 'Staff Shifts' (list format), 'Staff Calendar' (calendar format), and 'Scheduled vs Required' (comparison)")

        # Report understaffing errors
        if understaffed_hours:
            print(f"\n⚠️  ERROR: UNDERSTAFFING DETECTED!")
            print(f"The following hours have insufficient staff:")
            for hour, required, scheduled, shortage in understaffed_hours:
                print(f"  Hour {hour:2d}: Required {required}, Scheduled {scheduled}, Short by {shortage}")
            print(f"\nTotal understaffed hours: {len(understaffed_hours)}")
            print(f"Consider increasing MAX_SHIFTS_PER_STAFF or NUM_STAFF to resolve understaffing.")
        else:
            print(f"✅ All staffing requirements met successfully!")

        return assignments

    elif status == cp_model.INFEASIBLE:
        print('No solution found. Try increasing MAX_SHIFTS_PER_STAFF or NUM_STAFF.')
        return []
    else:
        print('Error occurred during solving.')
        return []

# --- Run the solver ---
if __name__ == "__main__":
    # Create single weekly workbook with all days
    create_weekly_workbook('Optix Shifts_Gideon.csv', 'weekly_schedule.xlsx')

    # Alternative: Process days individually
    # process_all_days_balanced('Optix Shifts_Gideon.csv')
    # process_all_days('Optix Shifts_Gideon.csv')

    # Alternative: Process individual days
    # solve_shift_scheduling('Optix Shifts_Gideon.csv', 'Monday', 'monday_shifts_output.xlsx')
    # solve_shift_scheduling()  # Use default data and default output filename
