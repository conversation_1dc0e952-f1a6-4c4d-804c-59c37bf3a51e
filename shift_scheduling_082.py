from ortools.sat.python import cp_model
from openpyxl import Workbook
from openpyxl.formula.translate import Translator
from openpyxl.worksheet.datavalidation import DataValidation
from openpyxl.styles import Font, PatternFill
from datetime import datetime, timedelta
from tqdm import tqdm
import csv
import argparse

PREFERRED_SHIFT_HOURS = 5  # change this to your preferred shift length
MAX_SHIFTS_PER_STAFF = 1  # maximum number of shifts a staff member can work per day

# Global scheduling constants
MIN_SHIFT_HOURS = 2
MAX_SHIFT_HOURS = 6
NUM_STAFF = 35  # total number of staff positions available
NUM_HOURS = 24  # hours in a day
DEFAULT_CSV_FILENAME = 'shift_names.csv'
DAYS_OF_WEEK = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']

# Break scheduling constants
BREAK_DURATION_MINUTES = 30  # 30 minute breaks
MIN_WORK_BEFORE_BREAK_HOURS = 1.5  # Must work 1.5 hours before break
MIN_WORK_AFTER_BREAK_HOURS = 1.5  # Must work 1.5 hours after break
MAX_CONCURRENT_BREAKS = 1  # Normally only 1 break at a time, worst case 2 can overlap

class BreakTimesResult:
    """Custom class to store both break times and violations"""
    def __init__(self, break_times, violations):
        self.break_times = break_times
        self._violations = violations
        
    def __contains__(self, key):
        return key in self.break_times
        
    def __getitem__(self, key):
        return self.break_times[key]
        
    def get(self, key, default=None):
        return self.break_times.get(key, default)
        
    def keys(self):
        return self.break_times.keys()
        
    def values(self):
        return self.break_times.values()
        
    def items(self):
        return self.break_times.items()

def load_staff_names(csv_filename=DEFAULT_CSV_FILENAME):
    """Load staff names from CSV file and return as a list"""
    staff_names = []
    try:
        with open(csv_filename, 'r') as csv_file:
            reader = csv.reader(csv_file)
            for row in reader:
                if row and row[0].strip():  # Skip empty rows
                    staff_names.append(row[0].strip().strip('"'))
    except FileNotFoundError:
        print(f"Warning: {csv_filename} not found. Using staff numbers instead.")
    return staff_names

def get_staff_display_name(staff_id, staff_names):
    """Get display name for staff - use name if available, otherwise use staff number"""
    if staff_id < len(staff_names):
        return staff_names[staff_id]
    else:
        return f"Staff {staff_id + 1}"

def get_all_staff_names_for_dropdown():
    """Get complete list of staff names for dropdown including named staff and Staff # entries, sorted alphabetically"""
    staff_names = load_staff_names()
    all_names = []
    
    # Add all named staff
    all_names.extend(staff_names)
    
    # Add Staff # entries for positions beyond named staff
    for i in range(len(staff_names), NUM_STAFF):
        all_names.append(f"Staff {i + 1}")
    
    # Sort all names alphabetically (case-insensitive)
    all_names.sort(key=str.lower)
    
    return all_names

def calculate_break_times(assignments):
    """
    Calculate optimal break times for staff shifts using OR-Tools CP-SAT solver.
    
    Args:
        assignments: List of tuples (start_time, staff_id, duration) representing shift assignments
        
    Returns:
        Dictionary mapping (staff_id, start_time) to break_start_time in minutes from shift start
        Returns None if no feasible solution exists
    """
    if not assignments:
        return {}
    
    # Check if any shifts are too short for breaks
    MINUTES_PER_HOUR = 60
    BREAK_DURATION = BREAK_DURATION_MINUTES
    MIN_WORK_BEFORE_BREAK = int(MIN_WORK_BEFORE_BREAK_HOURS * MINUTES_PER_HOUR)
    MIN_WORK_AFTER_BREAK = int(MIN_WORK_AFTER_BREAK_HOURS * MINUTES_PER_HOUR)
    
    valid_assignments = []
    for start_time, staff_id, duration in assignments:
        shift_duration_minutes = duration * MINUTES_PER_HOUR
        min_required_duration = MIN_WORK_BEFORE_BREAK + BREAK_DURATION + MIN_WORK_AFTER_BREAK
        
        if shift_duration_minutes >= min_required_duration:
            valid_assignments.append((start_time, staff_id, duration))
        else:
            print(f"Warning: Shift for staff {staff_id} starting at {start_time} ({duration}h) is too short for break constraints (need {min_required_duration/60:.1f}h)")
    
    if not valid_assignments:
        print("No shifts are long enough for break constraints")
        return {}
    
    print(f"Calculating breaks for {len(valid_assignments)} valid shifts (30-minute intervals)")
    
    # If we have very few shifts or want to avoid complexity, use simple calculation
    if len(valid_assignments) <= 3:
        print("Using simple break calculation for small number of shifts (30-minute intervals)")
        return calculate_simple_break_times(valid_assignments)
    
    # Try a simpler approach first - just ensure breaks don't overlap too much
    model = cp_model.CpModel()
    
    # Create variables for break start times (in minutes from shift start)
    break_vars = {}
    shift_info = {}
    
    for start_time, staff_id, duration in valid_assignments:
        shift_duration_minutes = duration * MINUTES_PER_HOUR
        shift_key = (staff_id, start_time)
        
        # Store shift information
        shift_info[shift_key] = {
            'start_time': start_time,
            'duration': duration,
            'duration_minutes': shift_duration_minutes,
            'absolute_start': start_time * MINUTES_PER_HOUR,
            'absolute_end': start_time * MINUTES_PER_HOUR + shift_duration_minutes
        }
        
        # Break must start between MIN_WORK_BEFORE_BREAK and (shift_duration - MIN_WORK_AFTER_BREAK - BREAK_DURATION)
        earliest_break = MIN_WORK_BEFORE_BREAK
        latest_break = shift_duration_minutes - MIN_WORK_AFTER_BREAK - BREAK_DURATION
        
        # Calculate valid 30-minute slots within the allowed window
        # Find the first 30-minute boundary at or after earliest_break
        earliest_slot = ((earliest_break + 29) // 30) * 30  # Round up to next 30-min boundary
        # Find the last 30-minute boundary at or before latest_break
        latest_slot = (latest_break // 30) * 30  # Round down to prev 30-min boundary
        
        if latest_slot >= earliest_slot:
            # Create list of valid 30-minute slot starts (in minutes from shift start)
            valid_slots = []
            slot = earliest_slot
            while slot <= latest_slot:
                valid_slots.append(slot)
                slot += 30
            
            if valid_slots:
                # Create variable that can only take values from valid 30-minute slots
                break_vars[shift_key] = model.NewIntVarFromDomain(
                    cp_model.Domain.FromValues(valid_slots),
                    f'break_start_staff_{staff_id}_shift_{start_time}'
                )
            else:
                print(f"Warning: No valid 30-minute slots for staff {staff_id} shift starting at {start_time}")
                continue
        else:
            print(f"Warning: No valid 30-minute slots for staff {staff_id} shift starting at {start_time}")
            continue
    
    # Add overlap constraints using time-slot approach
    # Divide the day into 30-minute slots and ensure max 2 breaks per slot
    SLOT_DURATION = 30  # 30-minute slots
    SLOTS_PER_DAY = (24 * 60) // SLOT_DURATION  # 48 slots in a day
    
    print("Adding overlap constraints for maximum 2 concurrent breaks")
    
    # For each 30-minute slot, count how many breaks are active
    for slot in range(SLOTS_PER_DAY):
        slot_start_minute = slot * SLOT_DURATION
        slot_end_minute = (slot + 1) * SLOT_DURATION
        
        breaks_in_slot = []
        
        for shift_key, break_var in break_vars.items():
            staff_id, shift_start = shift_key
            shift_start_minutes = shift_start * 60
            
            # Check if this shift could have a break in this time slot
            shift_end_minutes = shift_start_minutes + shift_info[shift_key]['duration_minutes']
            
            # Only consider shifts that overlap with this time slot
            if shift_start_minutes < slot_end_minute and shift_end_minutes > slot_start_minute:
                # Create boolean variable: does this break overlap with this slot?
                break_in_slot = model.NewBoolVar(f'break_in_slot_staff_{staff_id}_shift_{shift_start}_slot_{slot}')
                
                # Calculate absolute break start and end times
                break_abs_start = shift_start_minutes + break_var
                break_abs_end = break_abs_start + BREAK_DURATION
                
                # Break overlaps with slot if:
                # break_abs_start < slot_end_minute AND break_abs_end > slot_start_minute
                
                # Use intermediate variables to make constraints clearer
                break_starts_before_slot_end = model.NewBoolVar(f'break_starts_before_slot_end_{staff_id}_{shift_start}_{slot}')
                break_ends_after_slot_start = model.NewBoolVar(f'break_ends_after_slot_start_{staff_id}_{shift_start}_{slot}')
                
                # break_starts_before_slot_end is true if break_abs_start < slot_end_minute
                model.Add(break_abs_start < slot_end_minute).OnlyEnforceIf(break_starts_before_slot_end)
                model.Add(break_abs_start >= slot_end_minute).OnlyEnforceIf(break_starts_before_slot_end.Not())
                
                # break_ends_after_slot_start is true if break_abs_end > slot_start_minute
                model.Add(break_abs_end > slot_start_minute).OnlyEnforceIf(break_ends_after_slot_start)
                model.Add(break_abs_end <= slot_start_minute).OnlyEnforceIf(break_ends_after_slot_start.Not())
                
                # break_in_slot is true if both conditions are true
                model.AddBoolAnd([break_starts_before_slot_end, break_ends_after_slot_start]).OnlyEnforceIf(break_in_slot)
                model.AddBoolOr([break_starts_before_slot_end.Not(), break_ends_after_slot_start.Not()]).OnlyEnforceIf(break_in_slot.Not())
                
                breaks_in_slot.append(break_in_slot)
        
        # Constraint: At most MAX_CONCURRENT_BREAKS breaks can be active in this slot
        if breaks_in_slot:
            model.Add(sum(breaks_in_slot) <= MAX_CONCURRENT_BREAKS)
    
    print(f"Added overlap constraints for {SLOTS_PER_DAY} time slots")
    
    # Solve the model
    solver = cp_model.CpSolver()
    solver.parameters.max_time_in_seconds = 30.0  # Increase time limit for more complex model
    status = solver.Solve(model)
    
    if status in (cp_model.OPTIMAL, cp_model.FEASIBLE):
        # Extract break times
        break_times = {}
        for shift_key, break_var in break_vars.items():
            break_start_minutes = solver.Value(break_var)
            break_times[shift_key] = break_start_minutes
        
        # Create violations dict - CP-SAT solutions are assumed to be valid unless validation shows otherwise
        break_violations = {}
        validation_result = validate_break_overlaps(valid_assignments, break_times)
        
        # Mark violations for individual breaks that contribute to exceeding the limit
        break_violations = detect_individual_break_violations(valid_assignments, break_times)
        
        if validation_result['valid']:
            print(f"✓ Break scheduling successful with max {validation_result['max_overlaps']} concurrent breaks (limit: {MAX_CONCURRENT_BREAKS})")
        else:
            print(f"⚠ Warning: Solution has {validation_result['max_overlaps']} concurrent breaks (exceeds preferred limit of {MAX_CONCURRENT_BREAKS}, worst case allows 2)")
        
        # Use the module-level BreakTimesResult class
        return BreakTimesResult(break_times, break_violations)
    else:
        print(f"Break scheduling failed with status: {solver.StatusName(status)}")
        # Fallback: strict break scheduling that respects MAX_CONCURRENT_BREAKS
        strict_breaks = calculate_strict_break_times(valid_assignments)
        validation_result = validate_break_overlaps(valid_assignments, strict_breaks)
        print(f"Using strict break calculation with max {validation_result['max_overlaps']} concurrent breaks")
        return strict_breaks

def calculate_simple_break_times(assignments):
    """
    Simple fallback break time calculation without complex overlap constraints.
    Places breaks at the midpoint of each shift.
    
    Args:
        assignments: List of tuples (start_time, staff_id, duration) representing shift assignments
        
    Returns:
        Dictionary mapping (staff_id, start_time) to break_start_time in minutes from shift start
    """
    MINUTES_PER_HOUR = 60
    BREAK_DURATION = BREAK_DURATION_MINUTES
    MIN_WORK_BEFORE_BREAK = int(MIN_WORK_BEFORE_BREAK_HOURS * MINUTES_PER_HOUR)
    MIN_WORK_AFTER_BREAK = int(MIN_WORK_AFTER_BREAK_HOURS * MINUTES_PER_HOUR)
    
    break_times = {}
    
    for start_time, staff_id, duration in assignments:
        shift_duration_minutes = duration * MINUTES_PER_HOUR
        shift_key = (staff_id, start_time)
        
        # Calculate break timing constraints
        earliest_break = MIN_WORK_BEFORE_BREAK
        latest_break = shift_duration_minutes - MIN_WORK_AFTER_BREAK - BREAK_DURATION
        
        if latest_break >= earliest_break:
            # Find valid 30-minute slots within the allowed window
            earliest_slot = ((earliest_break + 29) // 30) * 30  # Round up to next 30-min boundary
            latest_slot = (latest_break // 30) * 30  # Round down to prev 30-min boundary
            
            if latest_slot >= earliest_slot:
                # Place break at the middle 30-minute slot if possible
                middle_slot = earliest_slot + ((latest_slot - earliest_slot) // 60) * 30
                break_times[shift_key] = middle_slot
            else:
                # No valid 30-minute slots, use earliest possible 30-minute boundary
                break_times[shift_key] = earliest_slot
        else:
            # Shift too short, use earliest 30-minute boundary
            earliest_slot = ((earliest_break + 29) // 30) * 30
            break_times[shift_key] = earliest_slot
    
    # Detect individual break violations properly
    break_violations = detect_individual_break_violations(assignments, break_times)
    
    # Use the module-level BreakTimesResult class
    return BreakTimesResult(break_times, break_violations)

def calculate_strict_break_times(assignments):
    """
    Strict break time calculation that guarantees MAX_CONCURRENT_BREAKS is never exceeded.
    Uses a greedy approach to assign breaks while respecting overlap constraints.
    
    Args:
        assignments: List of tuples (start_time, staff_id, duration) representing shift assignments
        
    Returns:
        Dictionary mapping (staff_id, start_time) to break_start_minutes with violation flags
    """
    MINUTES_PER_HOUR = 60
    BREAK_DURATION = BREAK_DURATION_MINUTES
    MIN_WORK_BEFORE_BREAK = int(MIN_WORK_BEFORE_BREAK_HOURS * MINUTES_PER_HOUR)
    MIN_WORK_AFTER_BREAK = int(MIN_WORK_AFTER_BREAK_HOURS * MINUTES_PER_HOUR)
    
    break_times = {}
    break_violations = {}  # Track which breaks violate constraints
    
    # Sort assignments by shift start time to process in chronological order
    sorted_assignments = sorted(assignments, key=lambda x: x[0])
    
    # Track occupied break slots (30-minute intervals throughout the day)
    occupied_slots = {}  # slot_index -> count of breaks in that slot
    
    for start_time, staff_id, duration in sorted_assignments:
        shift_duration_minutes = duration * MINUTES_PER_HOUR
        shift_key = (staff_id, start_time)
        
        # Calculate break timing constraints
        earliest_break = MIN_WORK_BEFORE_BREAK
        latest_break = shift_duration_minutes - MIN_WORK_AFTER_BREAK - BREAK_DURATION
        
        if latest_break >= earliest_break:
            # Find valid 30-minute slots within the allowed window
            earliest_slot = ((earliest_break + 29) // 30) * 30
            latest_slot = (latest_break // 30) * 30
            
            if latest_slot >= earliest_slot:
                # Find the best available 30-minute slot
                best_slot = None
                best_slot_conflicts = float('inf')
                
                slot = earliest_slot
                while slot <= latest_slot:
                    # Calculate absolute time slot index for this break
                    abs_break_start = start_time * 60 + slot
                    slot_index = abs_break_start // 30
                    
                    # Count how many breaks would be active during this slot
                    conflicts = occupied_slots.get(slot_index, 0)
                    
                    # Also check the slot that this break would end in
                    abs_break_end = abs_break_start + BREAK_DURATION
                    end_slot_index = (abs_break_end - 1) // 30  # -1 because break ends at this minute
                    if end_slot_index != slot_index:
                        conflicts = max(conflicts, occupied_slots.get(end_slot_index, 0))
                    
                    # Choose slot with fewest conflicts, prioritizing no conflicts
                    if conflicts < best_slot_conflicts:
                        best_slot = slot
                        best_slot_conflicts = conflicts
                        
                        # If we found a slot with no conflicts, use it immediately
                        if conflicts == 0:
                            break
                    # If conflicts are equal, prefer earlier slots for consistency
                    elif conflicts == best_slot_conflicts and best_slot is None:
                        best_slot = slot
                        best_slot_conflicts = conflicts
                    
                    slot += 30
                
                if best_slot is not None:
                    break_times[shift_key] = best_slot
                    
                    # Update occupied slots
                    abs_break_start = start_time * 60 + best_slot
                    abs_break_end = abs_break_start + BREAK_DURATION
                    
                    # Mark all 30-minute slots that this break overlaps with
                    for minute in range(abs_break_start, abs_break_end):
                        slot_idx = minute // 30
                        occupied_slots[slot_idx] = occupied_slots.get(slot_idx, 0) + 1
                    
                    # Check if this break violates the preferred constraint (>1) or worst case (>2)
                    max_concurrent = max(occupied_slots.get(minute // 30, 0) for minute in range(abs_break_start, abs_break_end))
                    if max_concurrent > MAX_CONCURRENT_BREAKS:
                        break_violations[shift_key] = True
                        if max_concurrent > 2:
                            print(f"⚠ Break for staff {staff_id} at {start_time} exceeds worst case limit ({max_concurrent} > 2)")
                        else:
                            print(f"⚠ Break for staff {staff_id} at {start_time} exceeds preferred limit ({max_concurrent} > {MAX_CONCURRENT_BREAKS})")
                    else:
                        break_violations[shift_key] = False
                else:
                    # No valid slots found
                    earliest_slot = ((earliest_break + 29) // 30) * 30
                    break_times[shift_key] = earliest_slot
                    break_violations[shift_key] = True
                    print(f"⚠ No valid break slot for staff {staff_id} at {start_time}")
            else:
                # No valid 30-minute slots
                earliest_slot = ((earliest_break + 29) // 30) * 30
                break_times[shift_key] = earliest_slot
                break_violations[shift_key] = True
        else:
            # Shift too short
            earliest_slot = ((earliest_break + 29) // 30) * 30
            break_times[shift_key] = earliest_slot
            break_violations[shift_key] = True
    
    # Use the module-level BreakTimesResult class
    return BreakTimesResult(break_times, break_violations)

def validate_break_overlaps(assignments, break_times):
    """
    Validate that break times don't exceed the maximum concurrent breaks constraint.
    
    Args:
        assignments: List of tuples (start_time, staff_id, duration)
        break_times: Dictionary mapping (staff_id, start_time) to break_start_minutes
        
    Returns:
        Dictionary with 'valid' (bool) and 'max_overlaps' (int) keys
    """
    if not break_times:
        return {'valid': True, 'max_overlaps': 0}
    
    # Create list of break intervals with absolute times
    break_intervals = []
    for start_time, staff_id, duration in assignments:
        shift_key = (staff_id, start_time)
        if shift_key in break_times:
            # Handle both regular dict and BreakTimesResult object
            if hasattr(break_times, 'break_times'):
                break_start_minutes = break_times.break_times[shift_key]
            else:
                break_start_minutes = break_times[shift_key]
            break_start_abs = start_time * 60 + break_start_minutes
            break_end_abs = break_start_abs + BREAK_DURATION_MINUTES
            break_intervals.append((break_start_abs, break_end_abs, staff_id))
    
    # Check overlaps at each minute
    max_overlaps = 0
    for minute in range(24 * 60):  # Check each minute of the day
        overlaps = 0
        for break_start, break_end, staff_id in break_intervals:
            if break_start <= minute < break_end:
                overlaps += 1
        max_overlaps = max(max_overlaps, overlaps)
    
    return {
        'valid': max_overlaps <= MAX_CONCURRENT_BREAKS,
        'max_overlaps': max_overlaps
    }

def detect_individual_break_violations(assignments, break_times):
    """
    Detect which individual breaks contribute to constraint violations.
    
    Args:
        assignments: List of tuples (start_time, staff_id, duration)
        break_times: Dictionary or BreakTimesResult mapping (staff_id, start_time) to break_start_minutes
        
    Returns:
        Dictionary mapping (staff_id, start_time) to violation boolean
    """
    break_violations = {}
    
    # Create list of break intervals with absolute times
    break_intervals = []
    print(f"\n📋 Break Schedule Analysis for {len(assignments)} shifts:")
    for start_time, staff_id, duration in assignments:
        shift_key = (staff_id, start_time)
        if shift_key in break_times:
            # Handle both regular dict and BreakTimesResult object
            if hasattr(break_times, 'break_times'):
                break_start_minutes = break_times.break_times[shift_key]
            else:
                break_start_minutes = break_times[shift_key]
            break_start_abs = start_time * 60 + break_start_minutes
            break_end_abs = break_start_abs + BREAK_DURATION_MINUTES
            break_intervals.append((break_start_abs, break_end_abs, staff_id, start_time))
            
            # Format for display
            break_start_hour = break_start_abs // 60
            break_start_min = break_start_abs % 60
            break_end_hour = break_end_abs // 60
            break_end_min = break_end_abs % 60
            print(f"  Staff {staff_id}: Shift {start_time:02d}:00-{start_time+duration:02d}:00, Break {break_start_hour:02d}:{break_start_min:02d}-{break_end_hour:02d}:{break_end_min:02d}")
    
    # Find time periods where >MAX_CONCURRENT_BREAKS occur
    violation_periods = []
    for minute in range(24 * 60):  # Check each minute of the day
        concurrent_breaks = 0
        active_breaks = []
        for break_start, break_end, staff_id, shift_start in break_intervals:
            if break_start <= minute < break_end:
                concurrent_breaks += 1
                active_breaks.append((staff_id, shift_start))
        
        # If this minute exceeds the limit, record all active breaks as violating
        if concurrent_breaks > MAX_CONCURRENT_BREAKS:
            violation_periods.append((minute, active_breaks))
    
    # Mark breaks that participate in violation periods
    for minute, active_breaks in violation_periods:
        hour = minute // 60
        min_part = minute % 60
        print(f"⚠ Violation at {hour:02d}:{min_part:02d} - {len(active_breaks)} concurrent breaks (limit: {MAX_CONCURRENT_BREAKS})")
        for staff_id, shift_start in active_breaks:
            shift_key = (staff_id, shift_start)
            break_violations[shift_key] = True
            print(f"  - Staff {staff_id} (shift starts {shift_start:02d}:00) marked as violation")
    
    # Initialize all other shifts as no violation
    for start_time, staff_id, duration in assignments:
        shift_key = (staff_id, start_time)
        if shift_key not in break_violations:
            break_violations[shift_key] = False
    
    return break_violations

def format_break_time(shift_start_hour, break_start_minutes):
    """
    Convert break start time from minutes-from-shift-start to HH:MM format.
    Ensures break times are formatted to 30-minute intervals.
    
    Args:
        shift_start_hour: Hour when shift starts (0-23)
        break_start_minutes: Minutes from shift start when break begins
        
    Returns:
        String in HH:MM format representing break start time (always :00 or :30)
    """
    # Calculate absolute break start time
    break_start_hour = shift_start_hour + (break_start_minutes // 60)
    break_start_minute = break_start_minutes % 60
    
    # Ensure minute is either 0 or 30 (round to nearest 30-minute boundary)
    if break_start_minute < 15:
        break_start_minute = 0
    elif break_start_minute < 45:
        break_start_minute = 30
    else:
        break_start_minute = 0
        break_start_hour += 1
    
    # Handle day overflow (though unlikely in 24-hour shifts)
    break_start_hour = break_start_hour % 24
    
    return f"{break_start_hour:02d}:{break_start_minute:02d}"

def apply_break_violation_formatting(ws, row_num, break_column, has_violation):
    """
    Apply red formatting to break time cell if there's a constraint violation.
    
    Args:
        ws: Worksheet object
        row_num: Row number of the cell
        break_column: Column number for break time (1-indexed)
        has_violation: Boolean indicating if there's a violation
    """
    if has_violation:
        break_cell = ws.cell(row=row_num, column=break_column)
        break_cell.fill = PatternFill(start_color="FFCCCC", end_color="FFCCCC", fill_type="solid")
        break_cell.font = Font(color="CC0000", bold=True)

def format_time_from_minutes(total_minutes):
    """
    Convert total minutes from midnight to HH:MM format.
    
    Args:
        total_minutes: Total minutes from midnight (0-1439)
        
    Returns:
        String in HH:MM format
    """
    hours = (total_minutes // 60) % 24  # Handle overflow past 24 hours
    minutes = total_minutes % 60
    
    # Round to nearest 30-minute boundary for consistency
    if minutes < 15:
        minutes = 0
    elif minutes < 45:
        minutes = 30
    else:
        minutes = 0
        hours = (hours + 1) % 24
    
    return f"{hours:02d}:{minutes:02d}"

def get_overlapping_breaks(current_staff_id, current_shift_start, assignments, break_times, staff_names):
    """
    Get detailed information about break overlaps including specific overlap periods.
    
    Args:
        current_staff_id: ID of current staff member
        current_shift_start: Start time of current staff's shift
        assignments: List of all shift assignments
        break_times: Break times dictionary or BreakTimesResult object
        staff_names: List of staff names
        
    Returns:
        String showing overlapping staff and the specific overlap time periods
    """
    if not break_times:
        return ""
    
    current_shift_key = (current_staff_id, current_shift_start)
    if current_shift_key not in break_times:
        return ""
    
    # Get current staff's break time
    if hasattr(break_times, 'break_times'):
        current_break_minutes = break_times.break_times[current_shift_key]
    else:
        current_break_minutes = break_times[current_shift_key]
    
    current_break_start = current_shift_start * 60 + current_break_minutes
    current_break_end = current_break_start + BREAK_DURATION_MINUTES
    
    # Format current break time for display
    current_break_time = format_break_time(current_shift_start, current_break_minutes)
    current_break_end_time = format_time_from_minutes(current_break_end)
    
    overlapping_info = []
    
    # Check all other staff for overlaps
    for start_time, staff_id, duration in assignments:
        if staff_id == current_staff_id and start_time == current_shift_start:
            continue  # Skip self
            
        shift_key = (staff_id, start_time)
        if shift_key in break_times:
            # Get other staff's break time
            if hasattr(break_times, 'break_times'):
                other_break_minutes = break_times.break_times[shift_key]
            else:
                other_break_minutes = break_times[shift_key]
            
            other_break_start = start_time * 60 + other_break_minutes
            other_break_end = other_break_start + BREAK_DURATION_MINUTES
            
            # Check for overlap (any minute in common)
            if (current_break_start < other_break_end and other_break_start < current_break_end):
                staff_name = get_staff_display_name(staff_id, staff_names)
                other_break_time = format_break_time(start_time, other_break_minutes)
                other_break_end_time = format_time_from_minutes(other_break_end)
                
                # Calculate the exact overlap period
                overlap_start = max(current_break_start, other_break_start)
                overlap_end = min(current_break_end, other_break_end)
                overlap_start_time = format_time_from_minutes(overlap_start)
                overlap_end_time = format_time_from_minutes(overlap_end)
                
                # Format: "Staff Name: overlap_start-overlap_end (their break: break_start-break_end)"
                overlap_info = f"{staff_name}: {overlap_start_time}-{overlap_end_time} (break: {other_break_time}-{other_break_end_time})"
                overlapping_info.append(overlap_info)
    
    return "; ".join(overlapping_info) if overlapping_info else ""

def parse_monday_date(date_string):
    """Parse and validate Monday date from YYYYMMDD format"""
    try:
        # Parse the date string
        date_obj = datetime.strptime(date_string, '%Y%m%d')
        
        # Check if it's a Monday (weekday() returns 0 for Monday)
        if date_obj.weekday() != 0:
            raise ValueError(f"Date {date_string} is not a Monday. Please provide a Monday date.")
        
        return date_obj
    except ValueError as e:
        if "does not match format" in str(e):
            raise ValueError(f"Invalid date format. Please use YYYYMMDD format (e.g., 20250915)")
        else:
            raise e

def add_staff_dropdown_validation(workbook, worksheet, cell_range):
    """Add Excel-compatible dropdown validation for staff names to specified cell range (MS Office 2007 compatible)"""
    # Get all staff names for dropdown
    all_staff_names = get_all_staff_names_for_dropdown()
    
    # For MS Office 2007 compatibility, use direct list instead of sheet reference
    # Create comma-separated list of names (limited to 255 characters for Office 2007)
    staff_list = ','.join(all_staff_names)
    
    # If the list is too long for direct validation (>255 chars), use sheet reference method
    if len(staff_list) > 255:
        # Create or get the hidden validation sheet (fallback for long lists)
        validation_sheet_name = "StaffNames_Validation"
        
        if validation_sheet_name in workbook.sheetnames:
            validation_ws = workbook[validation_sheet_name]
        else:
            validation_ws = workbook.create_sheet(validation_sheet_name)
            validation_ws.sheet_state = 'hidden'  # Hide the sheet
            
            # Populate the validation sheet with staff names
            for idx, name in enumerate(all_staff_names, 1):
                validation_ws.cell(row=idx, column=1, value=name)
        
        # Create data validation referencing the hidden sheet
        staff_count = len(all_staff_names)
        validation_range = f"{validation_sheet_name}!$A$1:$A${staff_count}"
        dv = DataValidation(type="list", formula1=validation_range, allow_blank=False)
    else:
        # Use direct list method (more compatible with Office 2007)
        dv = DataValidation(type="list", formula1=f'"{staff_list}"', allow_blank=False)
    
    # Set validation messages
    dv.error = "Please select a valid staff name from the dropdown"
    dv.errorTitle = "Invalid Staff Name"
    dv.prompt = "Select a staff member from the dropdown list"
    dv.promptTitle = "Staff Selection"
    
    # Apply validation to the specified range
    worksheet.add_data_validation(dv)
    dv.add(cell_range)

def read_staff_requirements_from_csv(csv_filename, day_name):
    """Read staff requirements for a specific day from CSV file"""
    import csv
    with open(csv_filename, 'r') as csv_file:
        reader = csv.reader(csv_file)
        current_day = None
        staff_requirements = []
        
        for row in reader:
            if len(row) >= 1:
                # Check if this row contains a day name
                if row[0] in DAYS_OF_WEEK:
                    current_day = row[0]
                    if current_day == day_name:
                        staff_requirements = []
                # Check if this is a header row (Time, OPS)
                elif row[0] == 'Time' and len(row) >= 2:
                    continue  # Skip header rows
                # Check if this is a time/staff data row for our target day
                elif current_day == day_name and len(row) >= 2 and ':' in row[0]:
                    try:
                        staff_count = int(row[1]) if row[1] else 0
                        staff_requirements.append(staff_count)
                    except ValueError:
                        staff_requirements.append(0)
    
    return staff_requirements

def write_shift_schedule_to_xlsx(assignments, output_filename='staff_shifts_output.xlsx', monday_date=None):
    """Write shift assignments to Excel file in the format shown in the image"""
    
    # Load staff names
    staff_names = load_staff_names()
    
    # Use provided Monday date or current date in the format shown in the image (YYYYMMDD)
    if monday_date:
        current_date = monday_date.strftime('%Y%m%d')
    else:
        current_date = datetime.now().strftime('%Y%m%d')
    
    # Create a new workbook and get the active worksheet
    wb = Workbook()
    ws = wb.active
    ws.title = "Staff Shifts"
    
    # Write header rows
    ws.append(['Report', 'Staff Shifts', '', '', '', '', ''])
    ws.append(['Date', current_date, '', '', '', '', ''])
    ws.append(['Staff Name', 'Start Time', 'Rest Break Time', 'End Time', 'Total Hours', 'Shift Count', 'Break Overlaps'])
    
    # Sort assignments by start time first, then by staff ID to maintain chronological order
    sorted_assignments = sorted(assignments, key=lambda x: (x[0], x[1]))  # Sort by start time, then staff ID
    
    # Calculate optimal break times
    break_times = calculate_break_times(sorted_assignments)
    
    # Write assignments with calculated break times
    for idx, (start_time, staff_id, duration) in enumerate(sorted_assignments):
        end_time = start_time + duration
        # Format times as HH:MM
        start_time_formatted = f"{start_time:02d}:00"
        end_time_formatted = f"{end_time:02d}:00"
        staff_name = get_staff_display_name(staff_id, staff_names)
        
        # Calculate break time
        has_violation = False
        if break_times and (staff_id, start_time) in break_times:
            break_start_minutes = break_times[(staff_id, start_time)]
            break_time_formatted = format_break_time(start_time, break_start_minutes)
            
            # Check if this break violates constraints
            if hasattr(break_times, '_violations'):
                shift_key = (staff_id, start_time)
                has_violation = break_times._violations.get(shift_key, False)
        else:
            # Fallback to start time if break calculation failed
            break_time_formatted = start_time_formatted
            has_violation = True  # Mark as violation since calculation failed
        
        # Get overlapping breaks information
        overlapping_breaks = get_overlapping_breaks(staff_id, start_time, sorted_assignments, break_times, staff_names)
        
        # Calculate shift count for this staff member
        staff_shift_count = sum(1 for s_time, s_id, s_duration in sorted_assignments if s_id == staff_id)
        if staff_shift_count > 1:
            shift_count_text = "Multiple Shifts"
        else:
            shift_count_text = "Single Shift"
            
        ws.append([staff_name, start_time_formatted, break_time_formatted, end_time_formatted, duration, shift_count_text, overlapping_breaks])
        
    
    # Data is now written with shift count and break overlaps included
    data_start_row = 4  # First data row after headers
    data_end_row = data_start_row + len(sorted_assignments) - 1
    
    # Add dropdown validation to Staff Name column
    if sorted_assignments:  # Only add validation if there are assignments
        add_staff_dropdown_validation(wb, ws, f'A{data_start_row}:A{data_end_row}')
    
    # Save the workbook
    wb.save(output_filename)
    print(f"Shift schedule written to {output_filename}")

def write_staff_calendar_to_xlsx(assignments, output_filename='staff_calendar_output.xlsx', monday_date=None):
    """Write staff assignments in calendar format with hours as columns"""
    
    # Load staff names
    staff_names = load_staff_names()
    
    # Use provided Monday date or current date in the format shown in the image (YYYYMMDD)
    if monday_date:
        current_date = monday_date.strftime('%Y%m%d')
    else:
        current_date = datetime.now().strftime('%Y%m%d')
    
    if not assignments:
        print("No assignments to write to calendar Excel file")
        return
    
    # Always use full 24-hour range (0-23)
    hour_range = list(range(24))
    
    # Create a mapping of hour -> list of staff working that hour
    hour_to_staff = {hour: [] for hour in hour_range}
    
    # Sort assignments and create staff formula mapping
    sorted_assignments = sorted(assignments, key=lambda x: (x[0], x[1]))
    
    for start_time, staff_id, duration in sorted_assignments:
        # Use direct cell reference to Scheduled Hours sheet
        staff_name = get_staff_display_name(staff_id, staff_names)
        
        # Add this staff formula to all hours they work
        for hour in range(start_time, start_time + duration):
            if hour in hour_to_staff:
                hour_to_staff[hour].append(staff_name)
    
    # Find maximum number of staff working in any single hour (for number of rows)
    max_staff_per_hour = max(len(staff_list) for staff_list in hour_to_staff.values()) if hour_to_staff else 0
    
    # Create a new workbook and get the active worksheet
    wb = Workbook()
    ws = wb.active
    ws.title = "Staff Calendar"
    
    # Write header rows
    header_row1 = ['Report', 'Staff Calendar'] + [''] * (len(hour_range) - 1)
    header_row2 = ['Date', current_date] + [''] * (len(hour_range) - 1)
    ws.append(header_row1)
    ws.append(header_row2)
    
    # Write hour headers - hours start from column A
    ws.append(hour_range)
    
    # Write staff assignments for each row
    for row_idx in range(max_staff_per_hour):
        row = []  # Start from column A
        for hour in hour_range:
            staff_list = hour_to_staff[hour]
            if row_idx < len(staff_list):
                row.append(staff_list[row_idx])
            else:
                row.append('')  # Empty cell if no staff at this position
        ws.append(row)
    
    # Save the workbook
    wb.save(output_filename)
    print(f"Staff calendar written to {output_filename}")

def write_combined_staff_workbook(assignments, staff_needed_per_hour, output_filename='staff_schedule.xlsx', monday_date=None):
    """Write both shift schedule and calendar to a single Excel workbook with separate sheets"""
    
    # Load staff names
    staff_names = load_staff_names()
    
    # Use provided Monday date or current date in the format shown in the image (YYYYMMDD)
    if monday_date:
        current_date = monday_date.strftime('%Y%m%d')
    else:
        current_date = datetime.now().strftime('%Y%m%d')
    
    if not assignments:
        print("No assignments to write to Excel workbook")
        return
    
    # Create a new workbook
    wb = Workbook()
    
    # === SHEET 1: Staff Shifts (List Format) ===
    ws_shifts = wb.active
    ws_shifts.title = "Staff Shifts"
    
    # Write header rows for shifts sheet
    ws_shifts.append(['Report', 'Staff Shifts', '', '', '', '', ''])
    ws_shifts.append(['Date', current_date, '', '', '', '', ''])
    ws_shifts.append(['Staff Name', 'Start Time', 'Rest Break Time', 'End Time', 'Total Hours', 'Shift Count', 'Break Overlaps'])
    
    # Sort assignments by start time first, then by staff ID to maintain chronological order
    sorted_assignments = sorted(assignments, key=lambda x: (x[0], x[1]))
    
    # Calculate optimal break times
    break_times = calculate_break_times(sorted_assignments)
    
    # Write assignments with calculated break times
    for idx, (start_time, staff_id, duration) in enumerate(sorted_assignments):
        end_time = start_time + duration
        # Format times as HH:MM
        start_time_formatted = f"{start_time:02d}:00"
        end_time_formatted = f"{end_time:02d}:00"
        # Use direct cell reference to Scheduled Hours sheet
        staff_name = get_staff_display_name(staff_id, staff_names)
        
        # Calculate break time
        has_violation = False
        if break_times and (staff_id, start_time) in break_times:
            break_start_minutes = break_times[(staff_id, start_time)]
            break_time_formatted = format_break_time(start_time, break_start_minutes)
            
            # Check if this break violates constraints
            if hasattr(break_times, '_violations'):
                shift_key = (staff_id, start_time)
                has_violation = break_times._violations.get(shift_key, False)
        else:
            # Fallback to start time if break calculation failed
            break_time_formatted = start_time_formatted
            has_violation = True  # Mark as violation since calculation failed
        
        # Get overlapping breaks information
        overlapping_breaks = get_overlapping_breaks(staff_id, start_time, sorted_assignments, break_times, staff_names)
        
        # Calculate shift count for this staff member
        staff_shift_count = sum(1 for s_time, s_id, s_duration in sorted_assignments if s_id == staff_id)
        if staff_shift_count > 1:
            shift_count_text = "Multiple Shifts"
        else:
            shift_count_text = "Single Shift"
            
        ws_shifts.append([staff_name, start_time_formatted, break_time_formatted, end_time_formatted, duration, shift_count_text, overlapping_breaks])
        
    
    # Data is now written with shift count and break overlaps included
    data_start_row = 4  # First data row after headers
    data_end_row = data_start_row + len(sorted_assignments) - 1
    
    # Add dropdown validation to Staff Name column
    if sorted_assignments:  # Only add validation if there are assignments
        add_staff_dropdown_validation(wb, ws_shifts, f'A{data_start_row}:A{data_end_row}')
    
    # === SHEET 2: Staff Calendar (Calendar Format) ===
    ws_calendar = wb.create_sheet("Staff Calendar")
    
    # Always use full 24-hour range (0-23)
    hour_range = list(range(24))
    
    # Create a mapping of hour -> list of (staff_name, shifts_row_number) working that hour
    hour_to_staff = {hour: [] for hour in hour_range}
    
    # Build mapping with row references to Staff Shifts sheet
    shifts_data_start_row = 4  # First data row in Staff Shifts sheet
    for idx, (start_time, staff_id, duration) in enumerate(sorted_assignments):
        staff_name = get_staff_display_name(staff_id, staff_names)
        shifts_row = shifts_data_start_row + idx
        
        # Add this staff with row reference to all hours they work
        for hour in range(start_time, start_time + duration):
            if hour in hour_to_staff:
                hour_to_staff[hour].append((staff_name, shifts_row))
    
    # Find maximum number of staff working in any single hour (for number of rows)
    max_staff_per_hour = max(len(staff_list) for staff_list in hour_to_staff.values()) if hour_to_staff else 0
    
    # Write header rows for calendar sheet
    header_row1 = ['Report', 'Staff Calendar'] + [''] * (len(hour_range) - 1)
    header_row2 = ['Date', current_date] + [''] * (len(hour_range) - 1)
    ws_calendar.append(header_row1)
    ws_calendar.append(header_row2)
    
    # Write hour headers - hours start from column A
    ws_calendar.append(hour_range)
    
    # Write staff assignments for each row using formulas that reference Staff Shifts sheet
    for row_idx in range(max_staff_per_hour):
        row = []  # Start from column A
        for hour in hour_range:
            staff_list = hour_to_staff[hour]
            if row_idx < len(staff_list):
                staff_name, shifts_row = staff_list[row_idx]
                # Create formula that references the Staff Shifts sheet
                formula = f"='Staff Shifts'!A{shifts_row}"
                row.append(formula)
            else:
                row.append('')  # Empty cell if no staff at this position
        ws_calendar.append(row)
    
    # === SHEET 3: Scheduled vs Required ===
    ws_comparison = wb.create_sheet("Scheduled vs Required")
    
    # Calculate scheduled staff per hour
    scheduled_per_hour = [0] * 24
    for start_time, staff_id, duration in sorted_assignments:
        for hour in range(start_time, start_time + duration):
            if hour < 24:
                scheduled_per_hour[hour] += 1
    
    # Write header rows for comparison sheet
    ws_comparison.append(['Report', 'Staff Scheduled vs Required'])
    ws_comparison.append(['Date', current_date])
    ws_comparison.append(['Hour of Day', 'Required', 'Scheduled', 'Difference'])
    
    # Write comparison data for each hour and check for understaffing
    understaffed_hours = []
    for hour in range(24):
        required = staff_needed_per_hour[hour] if hour < len(staff_needed_per_hour) else 0
        scheduled = scheduled_per_hour[hour]
        difference = scheduled - required
        ws_comparison.append([hour, required, scheduled, difference])
        
        # Track understaffed hours
        if scheduled < required:
            understaffed_hours.append((hour, required, scheduled, required - scheduled))
    
    # Add error information if understaffing detected
    if understaffed_hours:
        ws_comparison.append([])  # Empty row
        ws_comparison.append(['ERROR: UNDERSTAFFING DETECTED'])
        ws_comparison.append(['Hour', 'Required', 'Scheduled', 'Shortage'])
        for hour, required, scheduled, shortage in understaffed_hours:
            ws_comparison.append([f'Hour {hour}', required, scheduled, shortage])
    
    # === SHEET 4: Scheduled Hours ===
    ws_names = wb.create_sheet("Scheduled Hours")
    
    # Write header rows for scheduled hours sheet
    ws_names.append(['Report', 'Weekly Staff Scheduled Hours'])
    ws_names.append(['Start Date', current_date])
    
    # Calculate end date based on DAYS_OF_WEEK length
    if monday_date:
        end_date = monday_date + timedelta(days=len(DAYS_OF_WEEK) - 1)
        end_date_str = end_date.strftime('%Y%m%d')
    else:
        # If no Monday date provided, calculate end date from current date
        current_dt = datetime.now()
        days_since_monday = current_dt.weekday()
        monday_of_week = current_dt - timedelta(days=days_since_monday)
        end_date = monday_of_week + timedelta(days=len(DAYS_OF_WEEK) - 1)
        end_date_str = end_date.strftime('%Y%m%d')
    
    ws_names.append(['End Date', end_date_str])
    ws_names.append([])  # Empty row
    
    # Add column headers with bold formatting
    header_row = ws_names.max_row + 1
    ws_names.append(['Staff Name', 'Scheduled Hours'])
    
    # Apply bold formatting to column headers
    bold_font = Font(bold=True)
    ws_names.cell(row=header_row, column=1).font = bold_font  # Staff Name
    ws_names.cell(row=header_row, column=2).font = bold_font  # Scheduled Hours
    
    # Write staff names and their dynamically calculated scheduled hours
    for i in range(NUM_STAFF):
        staff_name = get_staff_display_name(i, staff_names)
        # Create SUMIF formula to sum hours from Staff Shifts sheet for this staff member
        # This will automatically update when names change in Staff Shifts
        hours_formula = f'=SUMIF(\'Staff Shifts\'!A:A,A{ws_names.max_row + 1},\'Staff Shifts\'!E:E)'
        ws_names.append([staff_name, hours_formula])
    
    # Save the workbook
    wb.save(output_filename)
    print(f"Combined staff workbook written to {output_filename}")
    print(f"Sheets created: 'Staff Shifts', 'Staff Calendar', 'Scheduled vs Required', and 'Scheduled Hours'")

def write_weekly_workbook(all_assignments, all_requirements, output_filename='weekly_schedule.xlsx', monday_date=None):
    """Write weekly schedule with all days Monday-Sunday in three sheets"""
    
    # Load staff names
    staff_names = load_staff_names()
    
    days_of_week = DAYS_OF_WEEK
    if monday_date:
        current_date = monday_date.strftime('%Y%m%d')
    else:
        current_date = datetime.now().strftime('%Y%m%d')
    
    # Create a new workbook
    wb = Workbook()
    
    # === SHEET 1: Staff Shifts (List Format) ===
    ws_shifts = wb.active
    ws_shifts.title = "Staff Shifts"
    
    # Write main header
    ws_shifts.append(['Report', 'Weekly Staff Shifts'])
    ws_shifts.append(['Date', current_date])
    ws_shifts.append([])  # Empty row
    
    # Store day information for later Shift Count column processing
    day_info = []
    
    # Process each day - populate all data first without Shift Count column
    for day_index, day in enumerate(days_of_week):
        assignments = all_assignments.get(day, [])
        
        # Calculate date for this day (Monday + day_index)
        if monday_date:
            day_date = monday_date + timedelta(days=day_index)
            day_date_str = day_date.strftime('%Y%m%d')
        else:
            # If no Monday date provided, use current date logic
            current_dt = datetime.now()
            # Find the Monday of current week
            days_since_monday = current_dt.weekday()
            monday_of_week = current_dt - timedelta(days=days_since_monday)
            day_date = monday_of_week + timedelta(days=day_index)
            day_date_str = day_date.strftime('%Y%m%d')
        
        # Day header with date
        day_header_row = ws_shifts.max_row + 1
        ws_shifts.append([day, day_date_str])
        ws_shifts.append(['Staff Name', 'Start Time', 'Rest Break Time', 'End Time', 'Total Hours', 'Shift Count', 'Break Overlaps'])
        
        data_start_row = ws_shifts.max_row + 1  # Two rows below weekday header
        
        # Calculate expected data_end_row before writing formulas
        assigned_count = len(assignments) if assignments else 0
        assigned_staff_ids = set(assignment[1] for assignment in assignments) if assignments else set()
        unassigned_count = NUM_STAFF - len(assigned_staff_ids)
        expected_data_end_row = data_start_row + assigned_count + unassigned_count - 1
        
        # Write staff assignments in the same order as Staff Calendar expects
        if assignments:
            # Sort assignments by start time, then staff ID (same as Staff Calendar)
            sorted_assignments = sorted(assignments, key=lambda x: (x[0], x[1]))
            
            # Calculate optimal break times for this day's assignments
            break_times = calculate_break_times(sorted_assignments)
            
            # Write assigned staff in the exact order Staff Calendar expects
            for idx, (start_time, staff_id, duration) in enumerate(sorted_assignments):
                staff_name = get_staff_display_name(staff_id, staff_names)
                end_time = start_time + duration
                start_time_formatted = f"{start_time:02d}:00"
                end_time_formatted = f"{end_time:02d}:00"
                
                # Calculate break time
                has_violation = False
                if break_times and (staff_id, start_time) in break_times:
                    break_start_minutes = break_times[(staff_id, start_time)]
                    break_time_formatted = format_break_time(start_time, break_start_minutes)
                    
                    # Check if this break violates constraints
                    if hasattr(break_times, '_violations'):
                        shift_key = (staff_id, start_time)
                        has_violation = break_times._violations.get(shift_key, False)
                else:
                    # Fallback to start time if break calculation failed
                    break_time_formatted = start_time_formatted
                    has_violation = True  # Mark as violation since calculation failed
                
                # Get overlapping breaks information
                overlapping_breaks = get_overlapping_breaks(staff_id, start_time, sorted_assignments, break_times, staff_names)
                
                row_num = data_start_row + idx
                # Add shift count formula that handles No Shift, Single Shift, Multiple Shifts
                shift_count_formula = f'=IF(E{row_num}=0,"No Shift",IF(COUNTIF(A{data_start_row}:A{expected_data_end_row},A{row_num})>1,"Multiple Shifts","Single Shift"))'
                ws_shifts.append([staff_name, start_time_formatted, break_time_formatted, end_time_formatted, duration, shift_count_formula, overlapping_breaks])
                
        
        # Add unassigned staff after the assigned ones
        unassigned_staff_count = 0
        for staff_id in range(NUM_STAFF):
            if staff_id not in assigned_staff_ids:
                staff_name = get_staff_display_name(staff_id, staff_names)
                row_num = data_start_row + assigned_count + unassigned_staff_count
                # Use same IF formula for unassigned staff (will evaluate to "No Shift" since E=0)
                shift_count_formula = f'=IF(E{row_num}=0,"No Shift",IF(COUNTIF(A{data_start_row}:A{expected_data_end_row},A{row_num})>1,"Multiple Shifts","Single Shift"))'
                ws_shifts.append([staff_name, '', '', '', 0, shift_count_formula, ''])
                unassigned_staff_count += 1
        
        data_end_row = ws_shifts.max_row
        
        # Store day information for Shift Count processing
        day_info.append({
            'day': day,
            'header_row': day_header_row + 1,  # Row with column headers
            'data_start_row': data_start_row,
            'data_end_row': data_end_row,
            'has_assignments': True  # Always true now since we show all staff
        })
        
        ws_shifts.append([])  # Empty row between days
    
    # Add dropdown validation to Staff Name column for each day's data
    for day_data in day_info:
        if day_data['has_assignments']:
            add_staff_dropdown_validation(wb, ws_shifts, f'A{day_data["data_start_row"]}:A{day_data["data_end_row"]}')
    
    # === SHEET 2: Staff Calendar (Calendar Format) ===
    ws_calendar = wb.create_sheet("Staff Calendar")
    
    # Write main header
    ws_calendar.append(['Report', 'Weekly Staff Calendar'])
    ws_calendar.append(['Date', current_date])
    ws_calendar.append([])  # Empty row
    
    # Hour range for headers
    hour_range = list(range(24))
    
    # Process each day using the day_info that tracks row numbers
    for day_index, day_data in enumerate(day_info):
        day = day_data['day']
        assignments = all_assignments.get(day, [])
        
        # Calculate date for this day (Monday + day_index)
        if monday_date:
            day_date = monday_date + timedelta(days=day_index)
            day_date_str = day_date.strftime('%Y%m%d')
        else:
            # If no Monday date provided, use current date logic
            current_dt = datetime.now()
            # Find the Monday of current week
            days_since_monday = current_dt.weekday()
            monday_of_week = current_dt - timedelta(days=days_since_monday)
            day_date = monday_of_week + timedelta(days=day_index)
            day_date_str = day_date.strftime('%Y%m%d')
        
        # Day header with date
        ws_calendar.append([day, day_date_str])
        
        # Hour headers
        ws_calendar.append(hour_range)
        
        if assignments and day_data['has_assignments']:
            # Create hour to staff mapping with row references
            hour_to_staff = {hour: [] for hour in hour_range}
            
            # Sort assignments and add staff with row references to working hours
            sorted_assignments = sorted(assignments, key=lambda x: (x[0], x[1]))
            
            for idx, (start_time, staff_id, duration) in enumerate(sorted_assignments):
                staff_name = get_staff_display_name(staff_id, staff_names)
                shifts_row = day_data['data_start_row'] + idx
                
                # Add staff with row reference to working hours
                for hour in range(start_time, start_time + duration):
                    if hour in hour_to_staff:
                        hour_to_staff[hour].append((staff_name, shifts_row))
            
            # Find max staff per hour
            max_staff_per_hour = max(len(staff_list) for staff_list in hour_to_staff.values()) if hour_to_staff else 0
            
            # Write staff assignments for each row using formulas that reference Staff Shifts sheet
            for row_idx in range(max_staff_per_hour):
                row = []
                for hour in hour_range:
                    staff_list = hour_to_staff[hour]
                    if row_idx < len(staff_list):
                        staff_name, shifts_row = staff_list[row_idx]
                        # Create formula that references the Staff Shifts sheet
                        formula = f"='Staff Shifts'!A{shifts_row}"
                        row.append(formula)
                    else:
                        row.append('')
                ws_calendar.append(row)
        else:
            # Empty row if no assignments
            ws_calendar.append(['No assignments'] + [''] * 23)
        
        ws_calendar.append([])  # Empty row between days
    
    # === SHEET 3: Scheduled vs Required ===
    ws_comparison = wb.create_sheet("Scheduled vs Required")
    
    # Write main header
    ws_comparison.append(['Report', 'Weekly Scheduled vs Required'])
    ws_comparison.append(['Date', current_date])
    ws_comparison.append([])  # Empty row
    
    # Process each day
    for day_index, day in enumerate(days_of_week):
        assignments = all_assignments.get(day, [])
        staff_needed_per_hour = all_requirements.get(day, [0] * 24)
        
        # Calculate date for this day (Monday + day_index)
        if monday_date:
            day_date = monday_date + timedelta(days=day_index)
            day_date_str = day_date.strftime('%Y%m%d')
        else:
            # If no Monday date provided, use current date logic
            current_dt = datetime.now()
            # Find the Monday of current week
            days_since_monday = current_dt.weekday()
            monday_of_week = current_dt - timedelta(days=days_since_monday)
            day_date = monday_of_week + timedelta(days=day_index)
            day_date_str = day_date.strftime('%Y%m%d')
        
        # Day header with date
        ws_comparison.append([day, day_date_str])
        ws_comparison.append(['Hour of Day', 'Required', 'Scheduled', 'Difference'])
        
        # Calculate scheduled staff per hour
        scheduled_per_hour = [0] * 24
        for start_time, staff_id, duration in assignments:
            for hour in range(start_time, start_time + duration):
                if hour < 24:
                    scheduled_per_hour[hour] += 1
        
        # Write comparison data
        understaffed_hours = []
        for hour in range(24):
            required = staff_needed_per_hour[hour] if hour < len(staff_needed_per_hour) else 0
            scheduled = scheduled_per_hour[hour]
            difference = scheduled - required
            ws_comparison.append([hour, required, scheduled, difference])
            
            # Track understaffed hours
            if scheduled < required:
                understaffed_hours.append((hour, required, scheduled, required - scheduled))
        
        # Add error information if understaffing detected
        if understaffed_hours:
            ws_comparison.append([])  # Empty row
            ws_comparison.append(['ERROR: UNDERSTAFFING DETECTED'])
            ws_comparison.append(['Hour', 'Required', 'Scheduled', 'Shortage'])
            for hour, required, scheduled, shortage in understaffed_hours:
                ws_comparison.append([f'Hour {hour}', required, scheduled, shortage])
        
        ws_comparison.append([])  # Empty row between days
    
    # === SHEET 4: Scheduled Hours ===
    ws_names = wb.create_sheet("Scheduled Hours")
    
    # Write header rows for scheduled hours sheet
    ws_names.append(['Report', 'Weekly Staff Scheduled Hours'])
    ws_names.append(['Start Date', current_date])
    
    # Calculate end date based on DAYS_OF_WEEK length
    if monday_date:
        end_date = monday_date + timedelta(days=len(DAYS_OF_WEEK) - 1)
        end_date_str = end_date.strftime('%Y%m%d')
    else:
        # If no Monday date provided, calculate end date from current date
        current_dt = datetime.now()
        days_since_monday = current_dt.weekday()
        monday_of_week = current_dt - timedelta(days=days_since_monday)
        end_date = monday_of_week + timedelta(days=len(DAYS_OF_WEEK) - 1)
        end_date_str = end_date.strftime('%Y%m%d')
    
    ws_names.append(['End Date', end_date_str])
    ws_names.append([])  # Empty row
    
    # Add column headers with bold formatting
    header_row = ws_names.max_row + 1
    ws_names.append(['Staff Name', 'Scheduled Hours'])
    
    # Apply bold formatting to column headers
    bold_font = Font(bold=True)
    ws_names.cell(row=header_row, column=1).font = bold_font  # Staff Name
    ws_names.cell(row=header_row, column=2).font = bold_font  # Scheduled Hours
    
    # Write staff names and their dynamically calculated scheduled hours
    for i in range(NUM_STAFF):
        staff_name = get_staff_display_name(i, staff_names)
        # Create SUMIF formula to sum hours from Staff Shifts sheet for this staff member
        # This will automatically update when names change in Staff Shifts
        hours_formula = f'=SUMIF(\'Staff Shifts\'!A:A,A{ws_names.max_row + 1},\'Staff Shifts\'!E:E)'
        ws_names.append([staff_name, hours_formula])
    
    # Save the workbook
    wb.save(output_filename)
    print(f"Weekly workbook written to {output_filename}")
    print(f"Sheets created: 'Staff Shifts', 'Staff Calendar', 'Scheduled vs Required', and 'Scheduled Hours' with all days Monday-Sunday")

# Complete the progress version of the function
def write_weekly_workbook_with_progress(all_assignments, all_requirements, output_filename='weekly_schedule.xlsx', pbar=None, monday_date=None):
    """Write weekly schedule with all days Monday-Sunday in three sheets with progress tracking"""
    
    # Load staff names
    staff_names = load_staff_names()
    
    days_of_week = DAYS_OF_WEEK
    if monday_date:
        current_date = monday_date.strftime('%Y%m%d')
    else:
        current_date = datetime.now().strftime('%Y%m%d')
    
    # Create a new workbook
    wb = Workbook()
    
    # === SHEET 1: Staff Shifts (List Format) ===
    ws_shifts = wb.active
    ws_shifts.title = "Staff Shifts"
    
    # Write main header
    ws_shifts.append(['Report', 'Weekly Staff Shifts'])
    ws_shifts.append(['Date', current_date])
    ws_shifts.append([])  # Empty row
    
    # Store day information for later Shift Count column processing
    day_info = []
    
    # Process each day - populate all data first without Shift Count column
    for day_index, day in enumerate(days_of_week):
        assignments = all_assignments.get(day, [])
        
        # Calculate date for this day (Monday + day_index)
        if monday_date:
            day_date = monday_date + timedelta(days=day_index)
            day_date_str = day_date.strftime('%Y%m%d')
        else:
            # If no Monday date provided, use current date logic
            current_dt = datetime.now()
            # Find the Monday of current week
            days_since_monday = current_dt.weekday()
            monday_of_week = current_dt - timedelta(days=days_since_monday)
            day_date = monday_of_week + timedelta(days=day_index)
            day_date_str = day_date.strftime('%Y%m%d')
        
        # Day header with date
        day_header_row = ws_shifts.max_row + 1
        ws_shifts.append([day, day_date_str])
        ws_shifts.append(['Staff Name', 'Start Time', 'Rest Break Time', 'End Time', 'Total Hours', 'Shift Count', 'Break Overlaps'])
        
        data_start_row = ws_shifts.max_row + 1  # Two rows below weekday header
        
        # Calculate expected data_end_row before writing formulas
        assigned_count = len(assignments) if assignments else 0
        assigned_staff_ids = set(assignment[1] for assignment in assignments) if assignments else set()
        unassigned_count = NUM_STAFF - len(assigned_staff_ids)
        expected_data_end_row = data_start_row + assigned_count + unassigned_count - 1
        
        # Write staff assignments in the same order as Staff Calendar expects
        if assignments:
            # Sort assignments by start time, then staff ID (same as Staff Calendar)
            sorted_assignments = sorted(assignments, key=lambda x: (x[0], x[1]))
            
            # Calculate optimal break times for this day's assignments
            break_times = calculate_break_times(sorted_assignments)
            
            # Write assigned staff in the exact order Staff Calendar expects
            for idx, (start_time, staff_id, duration) in enumerate(sorted_assignments):
                staff_name = get_staff_display_name(staff_id, staff_names)
                end_time = start_time + duration
                start_time_formatted = f"{start_time:02d}:00"
                end_time_formatted = f"{end_time:02d}:00"
                
                # Calculate break time
                has_violation = False
                if break_times and (staff_id, start_time) in break_times:
                    break_start_minutes = break_times[(staff_id, start_time)]
                    break_time_formatted = format_break_time(start_time, break_start_minutes)
                    
                    # Check if this break violates constraints
                    if hasattr(break_times, '_violations'):
                        shift_key = (staff_id, start_time)
                        has_violation = break_times._violations.get(shift_key, False)
                else:
                    # Fallback to start time if break calculation failed
                    break_time_formatted = start_time_formatted
                    has_violation = True  # Mark as violation since calculation failed
                
                # Get overlapping breaks information
                overlapping_breaks = get_overlapping_breaks(staff_id, start_time, sorted_assignments, break_times, staff_names)
                
                row_num = data_start_row + idx
                # Add shift count formula that handles No Shift, Single Shift, Multiple Shifts
                shift_count_formula = f'=IF(E{row_num}=0,"No Shift",IF(COUNTIF(A{data_start_row}:A{expected_data_end_row},A{row_num})>1,"Multiple Shifts","Single Shift"))'
                ws_shifts.append([staff_name, start_time_formatted, break_time_formatted, end_time_formatted, duration, shift_count_formula, overlapping_breaks])
                
        
        # Add unassigned staff after the assigned ones
        unassigned_staff_count = 0
        for staff_id in range(NUM_STAFF):
            if staff_id not in assigned_staff_ids:
                staff_name = get_staff_display_name(staff_id, staff_names)
                row_num = data_start_row + assigned_count + unassigned_staff_count
                # Use same IF formula for unassigned staff (will evaluate to "No Shift" since E=0)
                shift_count_formula = f'=IF(E{row_num}=0,"No Shift",IF(COUNTIF(A{data_start_row}:A{expected_data_end_row},A{row_num})>1,"Multiple Shifts","Single Shift"))'
                ws_shifts.append([staff_name, '', '', '', 0, shift_count_formula, ''])
                unassigned_staff_count += 1
        
        data_end_row = ws_shifts.max_row
        
        # Store day information for Shift Count processing
        day_info.append({
            'day': day,
            'header_row': day_header_row + 1,  # Row with column headers
            'data_start_row': data_start_row,
            'data_end_row': data_end_row,
            'has_assignments': True  # Always true now since we show all staff
        })
        
        ws_shifts.append([])  # Empty row between days
    
    # Add dropdown validation to Staff Name column for each day's data
    for day_data in day_info:
        if day_data['has_assignments']:
            add_staff_dropdown_validation(wb, ws_shifts, f'A{day_data["data_start_row"]}:A{day_data["data_end_row"]}')
    
    if pbar:
        pbar.set_description("Creating Staff Calendar sheet")
        pbar.update(1)
    
    # === SHEET 2: Staff Calendar (Calendar Format) ===
    ws_calendar = wb.create_sheet("Staff Calendar")
    
    # Write main header
    ws_calendar.append(['Report', 'Weekly Staff Calendar'])
    ws_calendar.append(['Date', current_date])
    ws_calendar.append([])  # Empty row
    
    # Hour range for headers
    hour_range = list(range(24))
    
    # Process each day using the day_info that tracks row numbers
    for day_index, day_data in enumerate(day_info):
        day = day_data['day']
        assignments = all_assignments.get(day, [])
        
        # Calculate date for this day (Monday + day_index)
        if monday_date:
            day_date = monday_date + timedelta(days=day_index)
            day_date_str = day_date.strftime('%Y%m%d')
        else:
            # If no Monday date provided, use current date logic
            current_dt = datetime.now()
            # Find the Monday of current week
            days_since_monday = current_dt.weekday()
            monday_of_week = current_dt - timedelta(days=days_since_monday)
            day_date = monday_of_week + timedelta(days=day_index)
            day_date_str = day_date.strftime('%Y%m%d')
        
        # Day header with date
        ws_calendar.append([day, day_date_str])
        
        # Hour headers
        ws_calendar.append(hour_range)
        
        if assignments and day_data['has_assignments']:
            # Create hour to staff mapping with row references
            hour_to_staff = {hour: [] for hour in hour_range}
            
            # Sort assignments and add staff with row references to working hours
            sorted_assignments = sorted(assignments, key=lambda x: (x[0], x[1]))
            
            for idx, (start_time, staff_id, duration) in enumerate(sorted_assignments):
                staff_name = get_staff_display_name(staff_id, staff_names)
                shifts_row = day_data['data_start_row'] + idx
                
                # Add staff with row reference to working hours
                for hour in range(start_time, start_time + duration):
                    if hour in hour_to_staff:
                        hour_to_staff[hour].append((staff_name, shifts_row))
            
            # Find max staff per hour
            max_staff_per_hour = max(len(staff_list) for staff_list in hour_to_staff.values()) if hour_to_staff else 0
            
            # Write staff assignments for each row using formulas that reference Staff Shifts sheet
            for row_idx in range(max_staff_per_hour):
                row = []
                for hour in hour_range:
                    staff_list = hour_to_staff[hour]
                    if row_idx < len(staff_list):
                        staff_name, shifts_row = staff_list[row_idx]
                        # Create formula that references the Staff Shifts sheet
                        formula = f"='Staff Shifts'!A{shifts_row}"
                        row.append(formula)
                    else:
                        row.append('')
                ws_calendar.append(row)
        else:
            # Empty row if no assignments
            ws_calendar.append(['No assignments'] + [''] * 23)
        
        ws_calendar.append([])  # Empty row between days
    
    if pbar:
        pbar.set_description("Creating Scheduled vs Required sheet")
        pbar.update(1)
    
    # === SHEET 3: Scheduled vs Required ===
    ws_comparison = wb.create_sheet("Scheduled vs Required")
    
    # Write main header
    ws_comparison.append(['Report', 'Weekly Scheduled vs Required'])
    ws_comparison.append(['Date', current_date])
    ws_comparison.append([])  # Empty row
    
    # Process each day
    for day_index, day in enumerate(days_of_week):
        assignments = all_assignments.get(day, [])
        staff_needed_per_hour = all_requirements.get(day, [0] * 24)
        
        # Calculate date for this day (Monday + day_index)
        if monday_date:
            day_date = monday_date + timedelta(days=day_index)
            day_date_str = day_date.strftime('%Y%m%d')
        else:
            # If no Monday date provided, use current date logic
            current_dt = datetime.now()
            # Find the Monday of current week
            days_since_monday = current_dt.weekday()
            monday_of_week = current_dt - timedelta(days=days_since_monday)
            day_date = monday_of_week + timedelta(days=day_index)
            day_date_str = day_date.strftime('%Y%m%d')
        
        # Day header with date
        ws_comparison.append([day, day_date_str])
        ws_comparison.append(['Hour of Day', 'Required', 'Scheduled', 'Difference'])
        
        # Calculate scheduled staff per hour
        scheduled_per_hour = [0] * 24
        for start_time, staff_id, duration in assignments:
            for hour in range(start_time, start_time + duration):
                if hour < 24:
                    scheduled_per_hour[hour] += 1
        
        # Write comparison data
        understaffed_hours = []
        for hour in range(24):
            required = staff_needed_per_hour[hour] if hour < len(staff_needed_per_hour) else 0
            scheduled = scheduled_per_hour[hour]
            difference = scheduled - required
            ws_comparison.append([hour, required, scheduled, difference])
            
            # Track understaffed hours
            if scheduled < required:
                understaffed_hours.append((hour, required, scheduled, required - scheduled))
        
        # Add error information if understaffing detected
        if understaffed_hours:
            ws_comparison.append([])  # Empty row
            ws_comparison.append(['ERROR: UNDERSTAFFING DETECTED'])
            ws_comparison.append(['Hour', 'Required', 'Scheduled', 'Shortage'])
            for hour, required, scheduled, shortage in understaffed_hours:
                ws_comparison.append([f'Hour {hour}', required, scheduled, shortage])
        
        ws_comparison.append([])  # Empty row between days
    
    # === SHEET 4: Scheduled Hours ===
    ws_names = wb.create_sheet("Scheduled Hours")
    
    # Write header rows for scheduled hours sheet
    ws_names.append(['Report', 'Weekly Staff Scheduled Hours'])
    ws_names.append(['Start Date', current_date])
    
    # Calculate end date based on DAYS_OF_WEEK length
    if monday_date:
        end_date = monday_date + timedelta(days=len(DAYS_OF_WEEK) - 1)
        end_date_str = end_date.strftime('%Y%m%d')
    else:
        # If no Monday date provided, calculate end date from current date
        current_dt = datetime.now()
        days_since_monday = current_dt.weekday()
        monday_of_week = current_dt - timedelta(days=days_since_monday)
        end_date = monday_of_week + timedelta(days=len(DAYS_OF_WEEK) - 1)
        end_date_str = end_date.strftime('%Y%m%d')
    
    ws_names.append(['End Date', end_date_str])
    ws_names.append([])  # Empty row
    
    # Add column headers with bold formatting
    header_row = ws_names.max_row + 1
    ws_names.append(['Staff Name', 'Scheduled Hours'])
    
    # Apply bold formatting to column headers
    bold_font = Font(bold=True)
    ws_names.cell(row=header_row, column=1).font = bold_font  # Staff Name
    ws_names.cell(row=header_row, column=2).font = bold_font  # Scheduled Hours
    
    # Write staff names and their dynamically calculated scheduled hours
    for i in range(NUM_STAFF):
        staff_name = get_staff_display_name(i, staff_names)
        # Create SUMIF formula to sum hours from Staff Shifts sheet for this staff member
        # This will automatically update when names change in Staff Shifts
        hours_formula = f'=SUMIF(\'Staff Shifts\'!A:A,A{ws_names.max_row + 1},\'Staff Shifts\'!E:E)'
        ws_names.append([staff_name, hours_formula])
    
    # Save the workbook
    wb.save(output_filename)
    
    if pbar:
        pbar.set_description("Workbook saved successfully")

def extract_daily_shifts_for_break_calc(csv_filename='Optix Shifts_Gideon.csv', target_day=None):
    """
    Extract shift start and end times per day for break calculation.
    
    Args:
        csv_filename: CSV file with shift requirements
        target_day: Specific day to extract (e.g., 'Monday'), or None for all days
        
    Returns:
        Dictionary with day -> list of (staff_name, start_time, end_time) tuples
    """
    days_to_process = [target_day] if target_day else DAYS_OF_WEEK
    daily_shifts = {}
    staff_names = load_staff_names()
    
    print(f"Extracting shift data from {csv_filename}...")
    
    for day in days_to_process:
        print(f"Processing {day}...")
        
        try:
            # Get staff requirements for this day
            staff_needed_per_hour = read_staff_requirements_from_csv(csv_filename, day)
            if not staff_needed_per_hour:
                daily_shifts[day] = []
                continue
            
            # Solve scheduling for this day
            assignments = solve_shift_scheduling_balanced_no_output(csv_filename, day, {})
            
            if assignments:
                # Convert assignments to break_calc format
                shift_list = []
                for start_time, staff_id, duration in assignments:
                    staff_name = get_staff_display_name(staff_id, staff_names)
                    start_time_str = f"{start_time:02d}:00"
                    end_time_str = f"{start_time + duration:02d}:00"
                    shift_list.append((staff_name, start_time_str, end_time_str))
                
                daily_shifts[day] = shift_list
                print(f"  ✓ {len(shift_list)} shifts extracted for {day}")
            else:
                daily_shifts[day] = []
                print(f"  ✗ No shifts found for {day}")
                
        except Exception as e:
            daily_shifts[day] = []
            print(f"  ✗ Error processing {day}: {str(e)}")
    
    return daily_shifts

def format_shifts_for_break_calc_json(daily_shifts, output_file='daily_shifts.json'):
    """
    Format extracted shifts as JSON for break_calc.py consumption.
    
    Args:
        daily_shifts: Dictionary from extract_daily_shifts_for_break_calc()
        output_file: JSON file to write
        
    Returns:
        Path to the created JSON file
    """
    import json
    
    # Format for break_calc.py
    formatted_data = {}
    for day, shifts in daily_shifts.items():
        formatted_data[day] = [
            {
                "name": staff_name,
                "shift_start": start_time,
                "shift_end": end_time
            }
            for staff_name, start_time, end_time in shifts
        ]
    
    with open(output_file, 'w') as f:
        json.dump(formatted_data, f, indent=2)
    
    print(f"Shift data exported to {output_file}")
    return output_file

def create_weekly_workbook(csv_filename='Optix Shifts_Gideon.csv', output_filename='weekly_schedule.xlsx', monday_date=None):
    """Create a single workbook with all days Monday-Sunday in three sheets"""
    days_of_week = DAYS_OF_WEEK
    
    if monday_date:
        print(f"Creating weekly workbook from {csv_filename} for week starting {monday_date.strftime('%Y-%m-%d')}...")
    else:
        print(f"Creating weekly workbook from {csv_filename}...")
    print("=" * 50)
    
    # Store all assignments and requirements for each day
    all_assignments = {}  # day -> assignments
    all_requirements = {}  # day -> staff_needed_per_hour
    weekly_staff_hours = {}  # staff_id -> total_hours_worked
    
    # Process each day with progress bar
    with tqdm(total=len(days_of_week), desc="Processing days", unit="day") as pbar:
        for day in days_of_week:
            pbar.set_description(f"Processing {day}")
            
            try:
                # Get staff requirements for this day
                staff_needed_per_hour = read_staff_requirements_from_csv(csv_filename, day)
                if not staff_needed_per_hour:
                    staff_needed_per_hour = [0] * 24
                
                all_requirements[day] = staff_needed_per_hour
                
                # Solve scheduling for this day (start fresh each day)
                assignments = solve_shift_scheduling_balanced_no_output(csv_filename, day, {})
                
                if assignments:
                    all_assignments[day] = assignments
                    pbar.set_postfix(status="✓ Success")
                else:
                    all_assignments[day] = []
                    pbar.set_postfix(status="✗ No solution")
                    
            except Exception as e:
                all_assignments[day] = []
                all_requirements[day] = [0] * 24
                pbar.set_postfix(status=f"✗ Error: {str(e)[:20]}...")
            
            pbar.update(1)
    
    # Create the combined weekly workbook with progress
    print("\nGenerating Excel workbook...")
    with tqdm(total=3, desc="Creating sheets", unit="sheet") as pbar:
        pbar.set_description("Creating workbook structure")
        pbar.update(1)
        
        write_weekly_workbook_with_progress(all_assignments, all_requirements, output_filename, pbar, monday_date)
    
    print("\n" + "=" * 50)
    print(f"Weekly workbook created: {output_filename}")

def process_all_days_balanced(csv_filename='Optix Shifts_Gideon.csv'):
    """Process all days with balanced staff hours across the week"""
    days_of_week = DAYS_OF_WEEK
    
    print(f"Processing all days from {csv_filename} with balanced weekly scheduling...")
    print("=" * 50)
    
    # Track staff hours across the week
    weekly_staff_hours = {}  # staff_id -> total_hours_worked
    
    # Process with progress bar
    with tqdm(total=len(days_of_week), desc="Processing days", unit="day") as pbar:
        for day in days_of_week:
            pbar.set_description(f"Processing {day}")
            output_filename = f"{day.lower()}_shifts_schedule.xlsx"
            
            try:
                assignments = solve_shift_scheduling_balanced(csv_filename, day, output_filename, {})
                if assignments:
                    pbar.set_postfix(status="✓ Success")
                else:
                    pbar.set_postfix(status="✗ No solution")
            except Exception as e:
                pbar.set_postfix(status=f"✗ Error: {str(e)[:20]}...")
            
            pbar.update(1)
    
    print("\n" + "=" * 50)
    print("All days processed with balanced weekly scheduling.")

def process_all_days(csv_filename='Optix Shifts_Gideon.csv'):
    """Process all days of the week and create separate Excel files for each day"""
    days_of_week = DAYS_OF_WEEK
    
    print(f"Processing all days from {csv_filename}...")
    print("=" * 50)
    
    for day in days_of_week:
        print(f"\nProcessing {day}...")
        output_filename = f"{day.lower()}_shifts_schedule.xlsx"
        
        try:
            assignments = solve_shift_scheduling(csv_filename, day, output_filename)
            if assignments:
                print(f"✓ {day} schedule created successfully: {output_filename}")
            else:
                print(f"✗ No solution found for {day}")
                print(f"  Consider increasing MAX_SHIFTS_PER_STAFF or NUM_STAFF for {day}")
        except Exception as e:
            print(f"✗ Error processing {day}: {str(e)}")
            print(f"  This may indicate understaffing issues for {day}")
    
    print("\n" + "=" * 50)
    print("All days processed. Excel files created for each day with reusable weekly schedules.")

def solve_shift_scheduling_balanced_no_output(csv_filename=None, day_name=None, weekly_staff_hours=None):
    """Solve shift scheduling with weekly hour balancing without creating output file"""
    if weekly_staff_hours is None:
        weekly_staff_hours = {}
    
    # Load staff names to determine priority
    staff_names = load_staff_names()
    num_named_staff = len(staff_names)
    
    model = cp_model.CpModel()

    # Read staff requirements from CSV if provided, otherwise use default
    if csv_filename and day_name:
        staff_needed_per_hour = read_staff_requirements_from_csv(csv_filename, day_name)
        if not staff_needed_per_hour:
            staff_needed_per_hour = [0] * 24
    else:
        # Default fallback data
        staff_needed_per_hour = [
            0,# 00:00
            0,# 01:00
            2,# 02:00
            2,# 03:00
            5,# 04:00
            9,# 05:00
            13,# 06:00
            18,# 07:00
            15,# 08:00
            15,# 09:00
            13,# 10:00
            13,# 11:00
            12,# 12:00
            12,# 13:00
            5,# 14:00
            6,# 15:00
            7,# 16:00
            2,# 17:00
            1,# 18:00
            0,# 19:00
            0,# 20:00
            0,# 21:00
            0,# 22:00
            0# 23:00
        ]

    staff_range = range(NUM_STAFF)
    hour_range = range(NUM_HOURS)

    # --- VARIABLES ---
    shifts = {}
    for s in staff_range:
        for start in hour_range:
            for duration in range(MIN_SHIFT_HOURS, MAX_SHIFT_HOURS + 1):
                if start + duration <= NUM_HOURS:
                    shifts[(s, start, duration)] = model.NewBoolVar(
                        f'shift_s{s}_st{start}_d{duration}'
                    )

    # --- CONSTRAINTS ---
    for s in staff_range:
        model.Add(
            sum([
                shifts[(s, start, duration)]
                for start in hour_range
                for duration in range(MIN_SHIFT_HOURS, MAX_SHIFT_HOURS + 1)
                if start + duration <= NUM_HOURS
            ]) <= MAX_SHIFTS_PER_STAFF
        )

    # Ensure shifts don't overlap for the same staff member
    for s in staff_range:
        for h in hour_range:
            overlapping_shifts = []
            for start in hour_range:
                for duration in range(MIN_SHIFT_HOURS, MAX_SHIFT_HOURS + 1):
                    if start <= h < start + duration and start + duration <= NUM_HOURS:
                        if (s, start, duration) in shifts:
                            overlapping_shifts.append(shifts[(s, start, duration)])
            if overlapping_shifts:
                model.Add(sum(overlapping_shifts) <= 1)

    # SOFT CONSTRAINT: Try to meet exact staffing, but allow closest possible if exact is impossible
    shortage_vars = {}
    excess_vars = {}
    
    for h in hour_range:
        staff_on_duty = []
        for s in staff_range:
            for start in hour_range:
                for duration in range(MIN_SHIFT_HOURS, MAX_SHIFT_HOURS + 1):
                    if start <= h < start + duration:
                        if (s, start, duration) in shifts:
                            staff_on_duty.append(shifts[(s, start, duration)])
        
        shortage_vars[h] = model.NewIntVar(0, staff_needed_per_hour[h], f'shortage_{h}')
        excess_vars[h] = model.NewIntVar(0, NUM_STAFF, f'excess_{h}')
        
        model.Add(sum(staff_on_duty) + shortage_vars[h] - excess_vars[h] == staff_needed_per_hour[h])

    # --- OBJECTIVE ---
    objective_terms = []
    
    # PRIORITY 1: Minimize staffing deviations
    for h in hour_range:
        objective_terms.append(1000 * shortage_vars[h])
        objective_terms.append(100 * excess_vars[h])
    
    # PRIORITY 2: Prefer PREFERRED_SHIFT_HOURS
    for (s, start, duration) in shifts:
        shift_preference_cost = ((duration - PREFERRED_SHIFT_HOURS)**2 + 1) * shifts[(s, start, duration)]
        objective_terms.append(shift_preference_cost)
    
    # PRIORITY 3: Prioritize named staff over unnamed staff
    for s in staff_range:
        if s >= num_named_staff:  # Unnamed staff get penalty
            staff_priority_penalty = 50  # Penalty for using unnamed staff
            for start in hour_range:
                for duration in range(MIN_SHIFT_HOURS, MAX_SHIFT_HOURS + 1):
                    if (s, start, duration) in shifts:
                        objective_terms.append(staff_priority_penalty * shifts[(s, start, duration)])
    
    # PRIORITY 4: Prioritize staff in numerical order (0, 1, 2, etc.)
    for s in staff_range:
        staff_order_penalty = s  # Lower staff numbers get lower penalty
        
        for start in hour_range:
            for duration in range(MIN_SHIFT_HOURS, MAX_SHIFT_HOURS + 1):
                if (s, start, duration) in shifts:
                    objective_terms.append(staff_order_penalty * shifts[(s, start, duration)])
    
    model.Minimize(sum(objective_terms))

    # --- SOLVE ---
    solver = cp_model.CpSolver()
    status = solver.Solve(model)

    # --- OUTPUT ---
    if status in (cp_model.OPTIMAL, cp_model.FEASIBLE):
        assignments = []
        for s in staff_range:
            for start in hour_range:
                for duration in range(MIN_SHIFT_HOURS, MAX_SHIFT_HOURS + 1):
                    if (s, start, duration) in shifts and solver.Value(shifts[(s, start, duration)]) == 1:
                        assignments.append((start, s, duration))
        
        assignments.sort(key=lambda x: (x[0], x[1]))
        return assignments
    else:
        return []

def solve_shift_scheduling_balanced(csv_filename=None, day_name=None, output_xlsx_filename='staff_shifts_output.xlsx', weekly_staff_hours=None, monday_date=None):
    """Solve shift scheduling with weekly hour balancing"""
    if weekly_staff_hours is None:
        weekly_staff_hours = {}
    
    # Load staff names to determine priority
    staff_names = load_staff_names()
    num_named_staff = len(staff_names)
    
    model = cp_model.CpModel()

    # Read staff requirements from CSV if provided, otherwise use default
    if csv_filename and day_name:
        staff_needed_per_hour = read_staff_requirements_from_csv(csv_filename, day_name)
        print(f"Reading staff requirements for {day_name} from {csv_filename}")
    else:
        # Default fallback data
        staff_needed_per_hour = [
            0,# 00:00
            0,# 01:00
            2,# 02:00
            2,# 03:00
            5,# 04:00
            9,# 05:00
            13,# 06:00
            18,# 07:00
            15,# 08:00
            15,# 09:00
            13,# 10:00
            13,# 11:00
            12,# 12:00
            12,# 13:00
            5,# 14:00
            6,# 15:00
            7,# 16:00
            2,# 17:00
            1,# 18:00
            0,# 19:00
            0,# 20:00
            0,# 21:00
            0,# 22:00
            0# 23:00
        ]
        print("Using default staff requirements")
    
    print(f"Staff requirements: {staff_needed_per_hour}")
    print(f"Total demand hours: {sum(staff_needed_per_hour)}")
    print()

    staff_range = range(NUM_STAFF)
    hour_range = range(NUM_HOURS)

    # --- VARIABLES ---
    shifts = {}
    for s in staff_range:
        for start in hour_range:
            for duration in range(MIN_SHIFT_HOURS, MAX_SHIFT_HOURS + 1):
                if start + duration <= NUM_HOURS:
                    shifts[(s, start, duration)] = model.NewBoolVar(
                        f'shift_s{s}_st{start}_d{duration}'
                    )

    # --- CONSTRAINTS ---
    # Modified constraint: Allow each staff member to work up to MAX_SHIFTS_PER_STAFF shifts
    for s in staff_range:
        model.Add(
            sum([
                shifts[(s, start, duration)]
                for start in hour_range
                for duration in range(MIN_SHIFT_HOURS, MAX_SHIFT_HOURS + 1)
                if start + duration <= NUM_HOURS
            ]) <= MAX_SHIFTS_PER_STAFF
        )

    # Ensure shifts don't overlap for the same staff member
    for s in staff_range:
        for h in hour_range:
            overlapping_shifts = []
            for start in hour_range:
                for duration in range(MIN_SHIFT_HOURS, MAX_SHIFT_HOURS + 1):
                    if start <= h < start + duration and start + duration <= NUM_HOURS:
                        if (s, start, duration) in shifts:
                            overlapping_shifts.append(shifts[(s, start, duration)])
            if overlapping_shifts:
                model.Add(sum(overlapping_shifts) <= 1)

    # SOFT CONSTRAINT: Try to meet exact staffing, but allow closest possible if exact is impossible
    shortage_vars = {}
    excess_vars = {}
    
    for h in hour_range:
        staff_on_duty = []
        for s in staff_range:
            for start in hour_range:
                for duration in range(MIN_SHIFT_HOURS, MAX_SHIFT_HOURS + 1):
                    if start <= h < start + duration:
                        if (s, start, duration) in shifts:
                            staff_on_duty.append(shifts[(s, start, duration)])
        
        # Create shortage and excess variables for soft constraints
        shortage_vars[h] = model.NewIntVar(0, staff_needed_per_hour[h], f'shortage_{h}')
        excess_vars[h] = model.NewIntVar(0, NUM_STAFF, f'excess_{h}')
        
        # Soft constraint: scheduled + shortage - excess = required
        model.Add(sum(staff_on_duty) + shortage_vars[h] - excess_vars[h] == staff_needed_per_hour[h])

    # --- OBJECTIVE: Minimize staffing deviations first, then balance weekly hours ---
    objective_terms = []
    
    # PRIORITY 1: Minimize staffing deviations (shortage and excess)
    # Heavy penalty for not meeting exact requirements
    for h in hour_range:
        # Shortage is worse than excess, so higher penalty
        objective_terms.append(1000 * shortage_vars[h])  # Very high penalty for understaffing
        objective_terms.append(100 * excess_vars[h])     # Lower penalty for overstaffing
    
    # PRIORITY 2: Prefer PREFERRED_SHIFT_HOURS
    for (s, start, duration) in shifts:
        shift_preference_cost = ((duration - PREFERRED_SHIFT_HOURS)**2 + 1) * shifts[(s, start, duration)]
        objective_terms.append(shift_preference_cost)
    
    # PRIORITY 3: Prioritize named staff over unnamed staff
    for s in staff_range:
        if s >= num_named_staff:  # Unnamed staff get penalty
            staff_priority_penalty = 50  # Penalty for using unnamed staff
            for start in hour_range:
                for duration in range(MIN_SHIFT_HOURS, MAX_SHIFT_HOURS + 1):
                    if (s, start, duration) in shifts:
                        objective_terms.append(staff_priority_penalty * shifts[(s, start, duration)])
    
    # PRIORITY 4: Prioritize staff in numerical order (0, 1, 2, etc.)
    for s in staff_range:
        staff_order_penalty = s  # Lower staff numbers get lower penalty
        
        for start in hour_range:
            for duration in range(MIN_SHIFT_HOURS, MAX_SHIFT_HOURS + 1):
                if (s, start, duration) in shifts:
                    objective_terms.append(staff_order_penalty * shifts[(s, start, duration)])
    
    model.Minimize(sum(objective_terms))

    # --- SOLVE ---
    solver = cp_model.CpSolver()
    status = solver.Solve(model)

    # --- OUTPUT ---
    if status in (cp_model.OPTIMAL, cp_model.FEASIBLE):
        print('Solution found!')
        print(f'Total weighted cost: {solver.ObjectiveValue():.0f}')
        print(f'--- Staff Assignments (sorted by start time then staff id) ---')

        assignments = []
        for s in staff_range:
            for start in hour_range:
                for duration in range(MIN_SHIFT_HOURS, MAX_SHIFT_HOURS + 1):
                    if (s, start, duration) in shifts and solver.Value(shifts[(s, start, duration)]) == 1:
                        assignments.append((start, s, duration))

        # sort by start time then staff ID
        assignments.sort(key=lambda x: (x[0], x[1]))

        # Track staff usage for summary
        staff_shift_count = {}
        for start, s, duration in assignments:
            end = start + duration
            print(f'Staff {s}: Works from hour {start}:00 to {end}:00 ({duration} hours)')
            staff_shift_count[s] = staff_shift_count.get(s, 0) + 1

        assigned_staff = {s for _, s, _ in assignments}
        for s in staff_range:
            if s not in assigned_staff:
                print(f'Staff {s}: Off duty')

        # Summary statistics
        print(f'\n--- Summary ---')
        print(f'Total shifts assigned: {len(assignments)}')
        staff_with_multiple_shifts = sum(1 for count in staff_shift_count.values() if count > 1)
        print(f'Staff working multiple shifts: {staff_with_multiple_shifts}')
        
        if staff_with_multiple_shifts > 0:
            print('Staff working multiple shifts:')
            for s, count in staff_shift_count.items():
                if count > 1:
                    print(f'  Staff {s}: {count} shifts')

        # Calculate actual staffing and deviations
        scheduled_per_hour = [0] * 24
        for start_time, staff_id, duration in assignments:
            for hour in range(start_time, start_time + duration):
                if hour < 24:
                    scheduled_per_hour[hour] += 1
        
        # Get the actual shortage and excess values from the solver
        total_shortage = 0
        total_excess = 0
        understaffed_hours = []
        overstaffed_hours = []
        
        for hour in range(24):
            required = staff_needed_per_hour[hour] if hour < len(staff_needed_per_hour) else 0
            scheduled = scheduled_per_hour[hour]
            shortage = solver.Value(shortage_vars[hour])
            excess = solver.Value(excess_vars[hour])
            
            total_shortage += shortage
            total_excess += excess
            
            if shortage > 0:
                understaffed_hours.append((hour, required, scheduled, shortage))
            elif excess > 0:
                overstaffed_hours.append((hour, required, scheduled, excess))

        # Write results to combined Excel workbook with multiple sheets
        write_combined_staff_workbook(assignments, staff_needed_per_hour, output_xlsx_filename, monday_date)
        
        print(f"\nExcel workbook created: {output_xlsx_filename}")
        print(f"Contains sheets: 'Staff Shifts' (list format), 'Staff Calendar' (calendar format), and 'Scheduled vs Required' (comparison)")
        
        # Report staffing accuracy with closest possible results
        if total_shortage == 0 and total_excess == 0:
            print(f"✅ Perfect staffing match! All differences are 0.")
        else:
            print(f"\n📊 Closest possible staffing achieved:")
            print(f"Total shortage: {total_shortage} staff-hours")
            print(f"Total excess: {total_excess} staff-hours")
            
            if understaffed_hours:
                print(f"\nUnderstaffed hours (closest possible):")
                for hour, required, scheduled, shortage in understaffed_hours:
                    print(f"  Hour {hour:2d}: Required {required}, Scheduled {scheduled}, Short by {shortage}")
            
            if overstaffed_hours:
                print(f"\nOverstaffed hours:")
                for hour, required, scheduled, excess in overstaffed_hours:
                    print(f"  Hour {hour:2d}: Required {required}, Scheduled {scheduled}, Excess by {excess}")
            
            print(f"\n💡 This is the closest possible match given the constraints.")
        
        return assignments

    elif status == cp_model.INFEASIBLE:
        print('No solution found. The exact staffing requirements cannot be met.')
        print('Consider:')
        print('- Increasing MAX_SHIFTS_PER_STAFF')
        print('- Increasing NUM_STAFF')
        print('- Adjusting min/max shift hours')
        print('- Reviewing if the staffing requirements are feasible')
        return []
    else:
        print('Error occurred during solving.')
        return []

def solve_shift_scheduling(csv_filename=None, day_name=None, output_xlsx_filename='staff_shifts_output.xlsx', monday_date=None):
    # Load staff names to determine priority
    staff_names = load_staff_names()
    num_named_staff = len(staff_names)
    
    model = cp_model.CpModel()

    # Read staff requirements from CSV if provided, otherwise use default
    if csv_filename and day_name:
        staff_needed_per_hour = read_staff_requirements_from_csv(csv_filename, day_name)
        print(f"Reading staff requirements for {day_name} from {csv_filename}")
    else:
        # Default fallback data
        staff_needed_per_hour = [
            0,# 00:00
            0,# 01:00
            2,# 02:00
            2,# 03:00
            5,# 04:00
            9,# 05:00
            13,# 06:00
            18,# 07:00
            15,# 08:00
            15,# 09:00
            13,# 10:00
            13,# 11:00
            12,# 12:00
            12,# 13:00
            5,# 14:00
            6,# 15:00
            7,# 16:00
            2,# 17:00
            1,# 18:00
            0,# 19:00
            0,# 20:00
            0,# 21:00
            0,# 22:00
            0# 23:00
        ]
        print("Using default staff requirements")
    
    print(f"Staff requirements: {staff_needed_per_hour}")
    print(f"Total demand hours: {sum(staff_needed_per_hour)}")
    print()

    staff_range = range(NUM_STAFF)
    hour_range = range(NUM_HOURS)

    # --- VARIABLES ---
    shifts = {}
    for s in staff_range:
        for start in hour_range:
            for duration in range(MIN_SHIFT_HOURS, MAX_SHIFT_HOURS + 1):
                if start + duration <= NUM_HOURS:
                    shifts[(s, start, duration)] = model.NewBoolVar(
                        f'shift_s{s}_st{start}_d{duration}'
                    )

    # --- CONSTRAINTS ---
    # Modified constraint: Allow each staff member to work up to MAX_SHIFTS_PER_STAFF shifts
    for s in staff_range:
        model.Add(
            sum([
                shifts[(s, start, duration)]
                for start in hour_range
                for duration in range(MIN_SHIFT_HOURS, MAX_SHIFT_HOURS + 1)
                if start + duration <= NUM_HOURS
            ]) <= MAX_SHIFTS_PER_STAFF
        )

    # Ensure shifts don't overlap for the same staff member
    for s in staff_range:
        for h in hour_range:
            overlapping_shifts = []
            for start in hour_range:
                for duration in range(MIN_SHIFT_HOURS, MAX_SHIFT_HOURS + 1):
                    if start <= h < start + duration and start + duration <= NUM_HOURS:
                        if (s, start, duration) in shifts:
                            overlapping_shifts.append(shifts[(s, start, duration)])
            if overlapping_shifts:
                model.Add(sum(overlapping_shifts) <= 1)

    # Ensure minimum staffing requirements are met
    for h in hour_range:
        staff_on_duty = []
        for s in staff_range:
            for start in hour_range:
                for duration in range(MIN_SHIFT_HOURS, MAX_SHIFT_HOURS + 1):
                    if start <= h < start + duration:
                        if (s, start, duration) in shifts:
                            staff_on_duty.append(shifts[(s, start, duration)])
        model.Add(sum(staff_on_duty) >= staff_needed_per_hour[h])

    # --- OBJECTIVE: prefer PREFERRED_SHIFT_HOURS and minimize total shifts ---
    objective_terms = []
    
    # PRIORITY 1: Prefer PREFERRED_SHIFT_HOURS
    for (s, start, duration) in shifts:
        shift_preference_cost = ((duration - PREFERRED_SHIFT_HOURS)**2 + 1) * shifts[(s, start, duration)]
        objective_terms.append(shift_preference_cost)
    
    # PRIORITY 2: Prioritize named staff over unnamed staff
    for s in staff_range:
        if s >= num_named_staff:  # Unnamed staff get penalty
            staff_priority_penalty = 50  # Penalty for using unnamed staff
            for start in hour_range:
                for duration in range(MIN_SHIFT_HOURS, MAX_SHIFT_HOURS + 1):
                    if start + duration <= NUM_HOURS and (s, start, duration) in shifts:
                        objective_terms.append(staff_priority_penalty * shifts[(s, start, duration)])
    
    # PRIORITY 3: Prioritize staff in numerical order (0, 1, 2, etc.)
    for s in staff_range:
        staff_order_penalty = s  # Lower staff numbers get lower penalty
        for start in hour_range:
            for duration in range(MIN_SHIFT_HOURS, MAX_SHIFT_HOURS + 1):
                if start + duration <= NUM_HOURS and (s, start, duration) in shifts:
                    objective_terms.append(staff_order_penalty * shifts[(s, start, duration)])
    
    # PRIORITY 4: Minimize total shifts (penalty for using multiple shifts per staff member)
    for s in staff_range:
        for start in hour_range:
            for duration in range(MIN_SHIFT_HOURS, MAX_SHIFT_HOURS + 1):
                if start + duration <= NUM_HOURS and (s, start, duration) in shifts:
                    objective_terms.append(10 * shifts[(s, start, duration)])
    
    model.Minimize(sum(objective_terms))

    # --- SOLVE ---
    solver = cp_model.CpSolver()
    status = solver.Solve(model)

    # --- OUTPUT ---
    if status in (cp_model.OPTIMAL, cp_model.FEASIBLE):
        print('Solution found!')
        print(f'Total weighted cost: {solver.ObjectiveValue():.0f}')
        print(f'--- Staff Assignments (sorted by start time then staff id) ---')

        assignments = []
        for s in staff_range:
            for start in hour_range:
                for duration in range(MIN_SHIFT_HOURS, MAX_SHIFT_HOURS + 1):
                    if (s, start, duration) in shifts and solver.Value(shifts[(s, start, duration)]) == 1:
                        assignments.append((start, s, duration))

        # sort by start time then staff ID
        assignments.sort(key=lambda x: (x[0], x[1]))

        # Track staff usage for summary
        staff_shift_count = {}
        for start, s, duration in assignments:
            end = start + duration
            print(f'Staff {s}: Works from hour {start}:00 to {end}:00 ({duration} hours)')
            staff_shift_count[s] = staff_shift_count.get(s, 0) + 1

        assigned_staff = {s for _, s, _ in assignments}
        for s in staff_range:
            if s not in assigned_staff:
                print(f'Staff {s}: Off duty')

        # Summary statistics
        print(f'\n--- Summary ---')
        print(f'Total shifts assigned: {len(assignments)}')
        staff_with_multiple_shifts = sum(1 for count in staff_shift_count.values() if count > 1)
        print(f'Staff working multiple shifts: {staff_with_multiple_shifts}')
        
        if staff_with_multiple_shifts > 0:
            print('Staff working multiple shifts:')
            for s, count in staff_shift_count.items():
                if count > 1:
                    print(f'  Staff {s}: {count} shifts')

        # Check for understaffing before writing results
        scheduled_per_hour = [0] * 24
        for start_time, staff_id, duration in assignments:
            for hour in range(start_time, start_time + duration):
                if hour < 24:
                    scheduled_per_hour[hour] += 1
        
        understaffed_hours = []
        for hour in range(24):
            required = staff_needed_per_hour[hour] if hour < len(staff_needed_per_hour) else 0
            scheduled = scheduled_per_hour[hour]
            if scheduled < required:
                understaffed_hours.append((hour, required, scheduled, required - scheduled))
        
        # Write results to combined Excel workbook with multiple sheets
        write_combined_staff_workbook(assignments, staff_needed_per_hour, output_xlsx_filename, monday_date)
        
        print(f"\nExcel workbook created: {output_xlsx_filename}")
        print(f"Contains sheets: 'Staff Shifts' (list format), 'Staff Calendar' (calendar format), and 'Scheduled vs Required' (comparison)")
        
        # Report understaffing errors
        if understaffed_hours:
            print(f"\n⚠️  ERROR: UNDERSTAFFING DETECTED!")
            print(f"The following hours have insufficient staff:")
            for hour, required, scheduled, shortage in understaffed_hours:
                print(f"  Hour {hour:2d}: Required {required}, Scheduled {scheduled}, Short by {shortage}")
            print(f"\nTotal understaffed hours: {len(understaffed_hours)}")
            print(f"Consider increasing MAX_SHIFTS_PER_STAFF or NUM_STAFF to resolve understaffing.")
        else:
            print(f"✅ All staffing requirements met successfully!")
        
        return assignments

    elif status == cp_model.INFEASIBLE:
        print('No solution found. Try increasing MAX_SHIFTS_PER_STAFF or NUM_STAFF.')
        return []
    else:
        print('Error occurred during solving.')
        return []

# --- Run the solver ---
if __name__ == "__main__":
    # Set up command-line argument parsing
    parser = argparse.ArgumentParser(description='Generate staff shift schedules')
    parser.add_argument('-w', '--week', type=str, help='Monday date in YYYYMMDD format (e.g., 20250915)')
    parser.add_argument('-i', '--input', type=str, default='Optix Shifts_Gideon.csv', 
                       help='Input CSV file with shift requirements (default: Optix Shifts_Gideon.csv)')
    parser.add_argument('-o', '--output', type=str, default='weekly_schedule.xlsx',
                       help='Output Excel file name (default: weekly_schedule.xlsx)')
    parser.add_argument('--export-breaks', action='store_true',
                       help='Export shift data for break calculation (creates daily_shifts.json)')
    parser.add_argument('--break-day', type=str, choices=DAYS_OF_WEEK,
                       help='Export shifts for specific day only (use with --export-breaks)')
    
    args = parser.parse_args()
    
    # Parse and validate Monday date if provided
    monday_date = None
    if args.week:
        try:
            monday_date = parse_monday_date(args.week)
            print(f"Using Monday date: {monday_date.strftime('%Y-%m-%d')}")
        except ValueError as e:
            print(f"Error: {e}")
            exit(1)
    
    # Handle break calculation export
    if args.export_breaks:
        print("Exporting shift data for break calculation...")
        daily_shifts = extract_daily_shifts_for_break_calc(args.input, args.break_day)
        json_file = format_shifts_for_break_calc_json(daily_shifts)
        print(f"\n✅ Shift data exported to {json_file}")
        print(f"Use this with break_calc.py: python break_calc.py --input {json_file}")
        if args.break_day:
            print(f"Data contains shifts for {args.break_day} only")
        else:
            print(f"Data contains shifts for all days of the week")
        exit(0)
    
    # Create single weekly workbook with all days
    create_weekly_workbook(args.input, args.output, monday_date)
    
    # Alternative: Process days individually
    # process_all_days_balanced('Optix Shifts_Gideon.csv')
    # process_all_days('Optix Shifts_Gideon.csv')
    
    # Alternative: Process individual days
    # solve_shift_scheduling('Optix Shifts_Gideon.csv', 'Monday', 'monday_shifts_output.xlsx')
    # solve_shift_scheduling()  # Use default data and default output filename